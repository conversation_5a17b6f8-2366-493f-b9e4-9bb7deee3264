{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Dashboard to monitor a single agent in a VisualSwarm according to the github project with the same name", "editable": true, "gnetId": null, "graphTooltip": 0, "id": 3, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 17, "panels": [], "title": "System Monitoring", "type": "row"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80}, {"color": "red", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 0, "y": 1}, "id": 10, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "7.3.6", "targets": [{"groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "system_parameters", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["cpu_1"], "type": "field"}, {"params": [], "type": "last"}]], "tags": []}], "timeFrom": null, "timeShift": null, "title": "CPU 1 usage [%]", "type": "gauge"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80}, {"color": "red", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 2, "y": 1}, "id": 11, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "7.3.6", "targets": [{"groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "system_parameters", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["cpu_2"], "type": "field"}, {"params": [], "type": "last"}]], "tags": []}], "timeFrom": null, "timeShift": null, "title": "CPU 2 usage [%]", "type": "gauge"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80}, {"color": "red", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 4, "y": 1}, "id": 12, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "7.3.6", "targets": [{"groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "system_parameters", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["cpu_3"], "type": "field"}, {"params": [], "type": "last"}]], "tags": []}], "timeFrom": null, "timeShift": null, "title": "CPU 3 usage [%]", "type": "gauge"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80}, {"color": "red", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 6, "y": 1}, "id": 13, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "7.3.6", "targets": [{"groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "system_parameters", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["cpu_4"], "type": "field"}, {"params": [], "type": "last"}]], "tags": []}], "timeFrom": null, "timeShift": null, "title": "CPU 4 usage [%]", "type": "gauge"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80}, {"color": "red", "value": 90}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 8, "y": 1}, "id": 21, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "7.3.6", "targets": [{"groupBy": [], "measurement": "system_parameters", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["disk_percent"], "type": "field"}]], "tags": []}], "timeFrom": null, "timeShift": null, "title": "Mean Used Disk Space [%]", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80}, {"color": "red", "value": 90}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 12, "y": 1}, "id": 22, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "7.3.6", "targets": [{"groupBy": [], "measurement": "system_parameters", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["mem_percent"], "type": "field"}]], "tags": []}], "timeFrom": null, "timeShift": null, "title": "Mean Used Memory [%]", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 72}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 8, "x": 0, "y": 4}, "id": 23, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "7.3.6", "targets": [{"groupBy": [], "measurement": "system_parameters", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["cpu_temperature"], "type": "field"}]], "tags": []}], "timeFrom": null, "timeShift": null, "title": "CPU temperature [°C]", "type": "stat"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 15, "panels": [], "title": "Control Parameters", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "InfluxDB", "description": "The graph shows the time evolution of the agent's velocity real time", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 7, "gridPos": {"h": 7, "w": 11, "x": 0, "y": 9}, "hiddenSeries": false, "id": 4, "interval": "100ms", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "control_parameters", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["agent_velocity"], "type": "field"}, {"params": [], "type": "last"}]], "tags": []}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Agent Velocity", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "Agent Velocity", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": "Time", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "description": "Processing delay from capturing a raw image with camera module to get the corresponding control parameters according to this frame.", "fieldConfig": {"defaults": {"custom": {}, "decimals": 0, "mappings": [], "max": 1000, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 125}, {"color": "red", "value": 250}, {"color": "rgb(78, 11, 11)", "value": 500}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 14, "w": 5, "x": 11, "y": 9}, "id": 8, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "textMode": "value"}, "pluginVersion": "7.3.6", "targets": [{"groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "control_parameters", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["processing_delay"], "type": "field"}, {"params": [], "type": "last"}, {"params": ["*1000"], "type": "math"}]], "tags": []}], "timeFrom": null, "timeShift": null, "title": "Processing Delay [ms]", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "InfluxDB", "description": "Heading direction of the agent", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 6, "gridPos": {"h": 7, "w": 11, "x": 0, "y": 16}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "control_parameters", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["heading_angle"], "type": "field"}, {"params": [], "type": "last"}]], "tags": []}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Heading Direction", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "<PERSON><PERSON>", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 19, "panels": [{"cards": {"cardPadding": 0, "cardRound": 0}, "color": {"cardColor": "#73BF69", "colorScale": "linear", "colorScheme": "interpolate<PERSON><PERSON>s", "exponent": 0.5, "mode": "opacity"}, "dataFormat": "tsbuckets", "datasource": "InfluxDB", "description": "The graph shows the visual projection field of the agent, i.e. the field has a value of 255 if a target object is detected at the given coordinate and 0 otherwise. \n\nUnits and Axes:\n   - x axis: time (timestamp)\n   - y axis: absolute position on the camera sensor in abstract px", "fieldConfig": {"defaults": {"custom": {"align": null, "filterable": false}, "mappings": [{"from": "", "id": 1, "text": "", "to": "", "type": 2}], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 11, "x": 0, "y": 10}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 2, "legend": {"show": false}, "pluginVersion": "7.3.6", "reverseYBuckets": false, "targets": [{"alias": "$col", "groupBy": [], "hide": false, "limit": "", "measurement": "visual_projection_field", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(*) FROM \"visual_projection_field\" WHERE $timeFilter GROUP BY time($interval)", "rawQuery": false, "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["*"], "type": "field"}]], "slimit": "", "tags": [], "tz": ""}], "timeFrom": null, "timeShift": null, "title": "Visual Projection Field", "tooltip": {"show": true, "showHistogram": false}, "transparent": true, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": null, "format": "px", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "lower", "yBucketNumber": null, "yBucketSize": null}], "title": "Vision", "type": "row"}], "refresh": "500ms", "schemaVersion": 26, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1m", "to": "now"}, "timepicker": {"refresh_intervals": ["250ms", "500ms", "1s", "5s", "10s"]}, "timezone": "", "title": "Agent Monitoring Dashboard (VisualSwarm)", "uid": "RxiUOJZRz", "version": 31}