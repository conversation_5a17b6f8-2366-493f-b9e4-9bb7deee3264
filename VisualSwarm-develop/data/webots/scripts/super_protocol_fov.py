"""
Example metaprotocol of changing configuration programatically and calling webots
@description: <PERSON><PERSON><PERSON> to run a webots world file multiple times with different initial conditions and/or parameters.
"""
import os
import numpy as np

from pprint import pprint
from visualswarm.simulation_tools import webots_tools

KAPPA = 125
SIMULATION_TIME = 900
num_robots = 4
BATCH_NAME = f"{num_robots}BotsFOV"
# alphas = [0.25, 0.5, 0.75, 1, 1.25, 1.5]
# bethas = [0.1, 0.25, 0.5, 0.75, 1, 1.2]
alphas = [0.5]  # [0.5, 0.75, 1]
bethas = [0.1, 0.5, 0.75, 1, 1.5, 2]
FOVs = [6.28-(i+1)*0.25*np.pi for i in range(4)]
FOVs = [5.495, 4.71, 3.925, 3.14]

save_path = "/mnt/DATA/mezey/Seafile/SwarmRobotics/VisualSwarm Simulation Data"
save_path = os.path.join(save_path, BATCH_NAME)
base_path = "/home/<USER>/Webots_VSWRM/VisualSwarm/data/webots/VSWRM_WeBots_Project/controllers/VSWRM-controller/config1"

wbt_path = f"/home/<USER>/Webots_VSWRM/VisualSwarm/data/webots/VSWRM_WeBots_Project/worlds/VSWRM_{num_robots}Bots_6mArena.wbt"

robot_names = [f"robot{i}" for i in range(num_robots)]

behave_params_path = os.path.join(base_path, 'VAR_behavior_params.json')
initial_condition_path = os.path.join(base_path, 'VAR_initial_conditions.json')
env_config_path = os.path.join(base_path, 'VAR_environment_config.json')

donei = 1

# starting agents from same initial conditions for better comparability
# Change Initial conditions under initial_condition_path
position_type = {'type': 'uniform',
                 'lim': 2,
                 'center': [0, 0]}
orientation_type = {'type': 'uniform',
                    'lim': 0.5,  # strong initial polarization to avoid waiting time
                    'center': np.random.uniform(-0.35, 0.35)}
webots_tools.generate_robot_config(robot_names, position_type, orientation_type, initial_condition_path)
print("Generated robot config for all batch!")

for alpi, alpha_0 in enumerate(alphas):
    for beti, betha_0 in enumerate(bethas):
        for FOV in FOVs:

            EXPERIMENT_NAME = f"WALLS_An{alpha_0}_Bn{betha_0}_{num_robots}bots_FOV{FOV:.3f}"
            print(f"Simulating runs for experiment {EXPERIMENT_NAME} with alpha={alpha_0}, betha={betha_0}")


            # Generate behavior parameters under behave_params path
            behavior_params = {
                "GAM": 0.1,
                "V0": KAPPA,
                "ALP0": KAPPA * alpha_0,
                "ALP1": 0.0006,
                "ALP2": 0,
                "BET0": betha_0,
                "BET1": 0.0006,
                "BET2": 0,
                "KAP": 1
            }
            webots_tools.write_config(behavior_params, behave_params_path)
            print(f"\nGenerated behavior params as:\n")
            pprint(behavior_params)

            num_runs = 1
            print(f"Number of Runs: {num_runs}")

            for run_i in range(num_runs):
                # making base link via environmental variables between webots and this script
                # env_config_path should be the same in this script and the controller code in webots
                env_config_dict = {
                    'ENABLE_SIMULATION': str(int(True)),
                    'SHOW_VISION_STREAMS': str(int(False)),
                    'LOG_LEVEL': 'INFO',
                    'WEBOTS_LOG_PERFORMANCE': str(int(False)),
                    'SPARE_RESCOURCES': str(int(True)),
                    'BORDER_CONDITIONS': "Reality",
                    'WEBOTS_SAVE_SIMULATION_DATA': str(int(True)),
                    'WEBOTS_SAVE_SIMULATION_VIDEO': str(int(True)),  # save video automatically
                    'WEBOTS_SIM_SAVE_FOLDER': os.path.join(save_path, EXPERIMENT_NAME),
                    'PAUSE_SIMULATION_AFTER': str(SIMULATION_TIME),
                    'PAUSE_BEHAVIOR': 'Quit',  # important to quit when batch simulation scripts are used
                    'BEHAVE_PARAMS_JSON_PATH': behave_params_path,
                    'INITIAL_CONDITION_PATH': initial_condition_path,
                    'USE_ROBOT_PEN': str(int(True)),  # enable or disable robot pens
                    'ROBOT_FOV': str(FOV)
                }
                # if run_i < 3:
                #     env_config_dict['WEBOTS_SAVE_SIMULATION_VIDEO'] = str(int(True))
                webots_tools.write_config(env_config_dict, env_config_path)
                print(f"\nGenerated environment config as:\n")
                pprint(env_config_dict)

                # call webots to run world file
                print("\n\n ---------- NEW WEBOTS RUN ----------")
                os.system(f"WEBOTS_CONFBASEPATH={base_path} webots --mode=realtime --stdout --stderr --minimize {wbt_path}")
                print('Done simulation\n\n\n')
                print(f'PROGRESS: {(donei/(len(alphas)*len(bethas)*len(FOVs)))*100}%')
                donei += 1

print('Superprotocol Done!')
