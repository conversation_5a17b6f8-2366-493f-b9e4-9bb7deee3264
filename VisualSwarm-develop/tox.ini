[tox]
envlist =
    unittest,
    quality_checks

[testenv]
basepython = python3.7
setenv =
    PYTHONWARNINGS = once
    PYTHONDONTWRITEBYTECODE = 1
    READTHEDOCS = True
deps = .[test]

[testenv:quality_checks]
description = "Checks to ensure code quality"
commands =
    flake8 --ignore=E127,E722,E402 --exclude ./visualswarm/simulation_tools,./data,./.tox .
    bandit -x */test* -l -ii -r visualswarm
    safety check -i 40291

[testenv:unittest]
commands = python -m pytest

[testenv:unittest_ci]
commands =
    python -m pytest \
        --cov-report=xml:{toxinidir}/dist/coverage.xml \
        --junitxml={toxinidir}/dist/junit.xml
    python -m coverage report

[coverage:run]
omit =
    **/visualswarm/simulation_tools/**.py
    **/visualswarm/app_simulation.py
    **visualswarm/contrib/**.py
    **/tests/**.py
    **/data
    **/env.py

[coverage:report]
fail_under = 85
skip_empty = yes
show_missing = yes
exclude_lines =
    pragma: no cover
    pragma: simulation no cover
    except KeyboardInterrupt:

[flake8]
exclude =
    .git,
    .tox,
    env,
    __pycache__,
    dist,
    build,
    docs,
    env.py,
    venv
max-line-length = 120

[pytest]
addopts =
    --ff
    --color=yes
    --code-highlight=yes
    --cov=visualswarm
python_files = tests.py test_*.py *_tests.py tests.py
junit_family = xunit2
