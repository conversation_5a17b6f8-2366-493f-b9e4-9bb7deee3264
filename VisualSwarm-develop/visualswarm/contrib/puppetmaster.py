"""
@author: <PERSON><PERSON><PERSON>
@description: Parameters related to the puppetmaster submodule controlling a swarm of robots via SSH with fabric

example command on robot: ENABLE_CLOUD_LOGGING=0 ENABLE_CLOUD_STORAGE=0 SAVE_VISION_VIDEO=1 SHOW_VISION_STREAMS=1
FLIP_CAMERA=0 ROBOT_FOV=3.8 LOG_LEVEL=DEBUG SAVE_CNN_TRAINING_DATA=0 ROBOT_NAME=Robot2 vswrm-start-vision
"""

# To select only a few robots, uncomment
# selected_robots = [3, 8]

# select all
selected_robots = [i+1 for i in range(10)]

ALL_HOSTS = {'Robot1': '*************',
             'Robot2': '*************',
             'Robot3': '*************',
             'Robot4': '*************',
             'Robot5': '*************',
             'Robot6': '*************',
             'Robot7': '*************',
             'Robot8': '*************',
             'Robot9': '*************',
             'Robot10': '*************'}

HOSTS = {}
for rid in selected_robots:
    HOSTS[f"Robot{rid}"] = ALL_HOSTS[f"Robot{rid}"]


WEBCAM_HOSTS = {
    'Birdseye Cam': '***************'
}
UNAME = 'pi'
INSTALL_DIR = '/home/<USER>/VisualSwarm'
