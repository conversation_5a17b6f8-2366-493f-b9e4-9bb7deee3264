"""
@author: me<PERSON><PERSON>
@description: tools AFTER WeBots simulation and processing to transform, read, process, summarize, etc. collected data
"""
import os
import pickle  # nosec
import json
import numpy as np
import logging
import pandas as pd
from scipy.spatial.distance import pdist, squareform
from fastcluster import linkage
from scipy.cluster.hierarchy import dendrogram

logging.basicConfig()
logger = logging.getLogger(__name__)
logger.setLevel("INFO")


def load_VSWRM_data(path):
    array2return = []
    with open(path, 'rb') as f:
        while True:
            try:
                # TODO: check if data was generated by VSWRM and refuse to deserialize otherwise
                array2return.append(pickle.load(f))  # nosec
            except EOFError:
                break
    return np.array(array2return)


def find_min_t(data_path):
    """as both different runs and different robots can yield slightly different number of measurement points
    we should first find the minimum so that all others are limited to this minimum"""
    robots = [name for name in os.listdir(data_path) if os.path.isdir(os.path.join(data_path, name))]
    min_ts = []
    for i, robot_name in enumerate(robots):
        robot_folder = os.path.join(data_path, robot_name)

        if i == 0:
            runs = [name for name in os.listdir(robot_folder) if os.path.isdir(os.path.join(robot_folder, name))]
            num_runs = len(runs)

        t_lens = []
        for j, run_name in enumerate(runs):
            position_array = load_VSWRM_data(
                os.path.join(data_path, robot_name, run_name, f'{robot_name}_run{run_name}_pos.npy'))

            t_lens.append(len(position_array[:, 0]))

        min_ts.append(np.min(t_lens))

    return np.min(min_ts)


def is_summarized(data_path, experiment_name):
    """Checking if a given experiment is already summarized into data and summary files so we can skip the rather
    long summary time for already summed experiments"""
    is_summary = os.path.isfile(os.path.join(data_path, f'{experiment_name}_summaryp.json'))
    is_data = os.path.isfile(os.path.join(data_path, f'{experiment_name}_summaryd.npy'))
    if is_summary and is_data:
        logger.info(f"{experiment_name} is already summarized...")
        return True
    else:
        logger.info("Experiment not yet summarized")
        return False


def distance_from_walls(coordinate, wall_coordinates):
    """Measuring the closest distance of the current coordinates to the walls described with
    a set of coordinates in numpy array wall_coordinates with shape (2, N). First dimension is x/y, second is the
    coordinate number of the wall.
    """
    num_wall_points = wall_coordinates.shape[1]
    distances = np.zeros(num_wall_points)

    # calculate distances between current coordinate with all wall coordinate points
    # for wci in range(num_wall_points):
    #     distances[wci] = distance(coordinate, wall_coordinates[:, wci])

    coordinates = np.zeros_like(wall_coordinates)
    coordinates[0, :] = coordinate[0]
    coordinates[1, :] = coordinate[1]
    distances_vect = distance(coordinates, wall_coordinates)

    # finding wall point to which the agent is closest
    closestind = np.nanargmin(distances_vect)
    closestcoord = wall_coordinates[:, closestind]
    closestdist = distances_vect[closestind]

    return closestdist, closestcoord, closestind


def calculate_distances_from_walls(summary, data, wall_summary, wall_data, force_recalculate=False):
    """Measuring the mean closest distance of a set of coordinates to the walls described with
    a set of coordinates in numpy array wall_coordinates with shape (2, N). First dimension is x/y, second is the
    coordinate number of the wall."""
    save_path = os.path.join(summary['data_path'], f"{summary['experiment_name']}_walldata.npz")
    wall_coordinates = wall_data[0, 0, [1, 3], :]
    coordinates = data[:, :, [1, 3], :]

    if os.path.isfile(save_path) and not force_recalculate:
        print("File in target path for agent-wall distances already exists! No recalculation was"
              "requested, so data will be loaded from the npz file.")
        file_data = np.load(save_path)
        distances = file_data['agent_wall_distances']
        coords = file_data['closest_wall_coordinates']
    else:
        num_runs = coordinates.shape[0]
        num_agents = coordinates.shape[1]
        num_t = coordinates.shape[-1]
        distances = np.zeros((num_runs, num_agents, num_t))
        coords = np.zeros((num_runs, num_agents, 2, num_t))

        print("Calculating wall distances")
        for runi in range(num_runs):
            print(f"Run {runi}/{num_runs}:")
            for agent_id in range(num_agents):
                print(f"Agent {agent_id}/{num_agents}...")
                for t in range(num_t):
                    if t % 100 == 0:
                        print(f"{t / num_t * 100}%", sep='', end='\r', flush=True)
                    closestdist, closestcoord, _ = distance_from_walls(coordinates[runi, agent_id, :, t],
                                                                       wall_coordinates)
                    distances[runi, agent_id, t] = closestdist
                    coords[runi, agent_id, :, t] = closestcoord

        # Saving calculated data
        np.savez(save_path, agent_wall_distances=distances, closest_wall_coordinates=coords)

    return distances, coords, np.mean(distances)


def calculate_turning_rates(summary, data, turning_rate_trh=0.2, force_recalculate=False):
    """Calculating turning rates from orientation values for a single robot
    Turning rate values larger than a physically possible threshold will be set to 0 in the data
    as it is coming from tracking/measurement noise."""
    save_path = os.path.join(summary['data_path'], f"{summary['experiment_name']}_turningrates.npy")

    if os.path.isfile(save_path) and not force_recalculate:
        print("File in target path for truning rates already exists! No recalculation was "
              "requested, so data will be loaded from the npy file.")
        tr = np.load(save_path)
    else:
        ori = data[:, :, 4, :]
        # calculating turning rates
        tr = np.abs(np.diff(ori)) % (np.pi)
        # removing physically impossible outliers
        tr[tr > turning_rate_trh] = 0

        print(f"Saving turning rates in npy file under {save_path}")
        np.save(save_path, tr)

    return tr


def filter_high_velocity_points(valid_ts, abs_vel_m, runi, vel_thr=0.6):
    """Filtering extreme velocity timepoints from data as this results from wrong tracking"""
    valid_ts_new = [t for t in valid_ts if np.all(abs_vel_m[runi, :, t - 1] <= vel_thr)]
    return valid_ts_new


def filter_high_turningrate_points(valid_ts, turning_rates, runi, tr_thr=0.2):
    """Filtering extreme velocity timepoints from data as this results from wrong tracking"""
    valid_ts_new = [t for t in valid_ts if np.all(turning_rates[runi, :, t - 1] <= tr_thr)]
    return valid_ts_new


def return_validts_pol(mean_pol, pol_thr=0.8):
    """Returning those time points where the mean polarization of the swarm falls above a threshold"""
    return np.where(mean_pol >= pol_thr)[0]


def return_validts_iid(mean_iid, iid_of_interest=500, tolerance=250):
    """Returning those time points where the mean iid of the swarm falls around an iid_of_interest and within tolerance.
    iidm is a run specific iid matrix, i.e. has shape [num_robots, num_robots, time] and not
    [num_runs, num_robots, num_robots, time]"""
    return np.where(np.logical_and(mean_iid >= iid_of_interest - tolerance,
                                   mean_iid <= iid_of_interest + tolerance))[0]


def return_metrics_where_no_collision(summary, polarization_m, iid_m, runi, agent_refl_times, wall_refl_times,
                                      window_after=0,
                                      window_before=0, force_recalculate=False):
    """Calculating common metrics for time points where no collision was detected"""
    num_t = polarization_m.shape[-1]

    save_path = os.path.join(summary['data_path'],
                             f"{summary['experiment_name']}_validts_wa{window_after}_wb{window_before}_run{runi}.npy")

    if os.path.isfile(save_path) and not force_recalculate:
        print("File in target path for valid time points already exists! No recalculation was"
              "requested, so data will be loaded from the npy file.")
        valid_ts = np.load(save_path)

    else:
        # Joining wall and agent reflections
        all_refl_times = []
        if agent_refl_times is None:
            agent_refl_times = []
        if wall_refl_times is None:
            wall_refl_times = []
        agent_refl_times.extend(wall_refl_times)
        all_refl_times.extend(wall_refl_times)
        all_refl_times.extend(agent_refl_times)

        # Extending reflection timepoints before and after reflections
        t_ext = [0]
        for ti, t in enumerate(all_refl_times):
            # if t - window_before >= np.max(t_ext):
            t_ext.extend([min(max(0, te), num_t) for te in range(t - window_before, t + window_after)])
            # print(t_ext)
        all_refl_times.extend(t_ext)
        all_refl_times = list(set(all_refl_times))

        valid_ts = [t for t in range(0, num_t) if t not in all_refl_times]
        print(f"Len valid_ts after calc: {len(list(set(valid_ts)))}")
        np.save(save_path, valid_ts)

    # filtering data
    if agent_refl_times is not None:
        polarization_m = polarization_m[runi, :, :, valid_ts]
        iid_m = iid_m[runi, :, :, valid_ts]

    iidm_nan = iid_m.copy()
    for t in range(iidm_nan.shape[-1]):
        np.fill_diagonal(iidm_nan[:, :, t], None)

    min_iidm = np.nanmin(np.nanmin(iidm_nan, axis=1), axis=1)
    mean_iid = np.nanmean(np.nanmean(iidm_nan, axis=1), axis=1)

    mean_pol_vals = np.mean(np.mean(polarization_m, axis=1), axis=1)

    mean_pol_vals_long = np.zeros(num_t)
    mean_pol_vals_long[valid_ts] = mean_pol_vals

    min_iidm_long = np.zeros(num_t)
    min_iidm_long[valid_ts] = min_iidm
    mean_iid_long = np.zeros(num_t)
    mean_iid_long[valid_ts] = mean_iid

    return valid_ts, min_iidm_long, mean_iid_long, mean_pol_vals_long


def return_summary_data(summary, data, wall_data_tuple=None, runi=0, mov_avg_w=10,
                        wall_vic_thr=200, agent_dist_thr=275, force_recalculate=False, turn_thr=0.02):
    if wall_data_tuple is not None:
        wall_summary, wall_data = wall_data_tuple
        wall_coordinates = wall_data[0, 0, [1, 3], :]
    else:
        wall_coordinates = None

    num_robots = data.shape[1]
    # COM velocity
    print("Calculating COM velocity")
    com_vel = population_velocity(summary, data, force_recalculate=force_recalculate)
    m_com_vel = np.zeros_like(com_vel)
    m_com_vel[runi, int(mov_avg_w / 2):-int(mov_avg_w / 2) + 1] = moving_average(
        com_vel[runi, :], mov_avg_w)

    # absolute velocities
    abs_vel = np.abs(calculate_velocity(summary, data))
    # absolute and com velocity with moving average
    print("Moving average of absolute velocity!")
    m_abs_vel = np.zeros_like(abs_vel)
    for robi in range(num_robots):
        m_abs_vel[runi, robi, int(mov_avg_w / 2):-int(mov_avg_w / 2) + 1] = moving_average(
            abs_vel[runi, robi, :], mov_avg_w)

    # turning rates
    print("Calculate turning rates!")
    turning_rates = calculate_turning_rates(summary, data, force_recalculate=force_recalculate)
    # moving average of turning rates
    ma_turning_rates = np.zeros_like(turning_rates)
    for robi in range(num_robots):
        ma_turning_rates[runi, robi, int(mov_avg_w / 2):-int(mov_avg_w / 2) + 1] = moving_average(
            turning_rates[runi, robi, :], mov_avg_w)

    # inter_individual distances
    print("Calculate IID")
    iidm = calculate_interindividual_distance(summary, data, force_recalculate=force_recalculate)
    # min and mean interindividual distances
    iidm_nan = iidm.copy()
    for t in range(iidm_nan.shape[-1]):
        np.fill_diagonal(iidm_nan[runi, :, :, t], None)

    try:
        min_iidm = np.nanmin(np.nanmin(iidm_nan, axis=1), axis=1)
        mean_iid = np.nanmean(np.nanmean(iidm_nan, axis=1), axis=1)
    except:
        min_iidm = None
        mean_iid = None

    pm = calculate_ploarization_matrix(summary, data, force_recalculate=force_recalculate)
    mean_pol_vals = np.mean(np.mean(pm, axis=1), axis=1)
    mean_pol_vals = mean_pol_vals[runi, :]

    ord = calculate_order_parameter(summary, data, force_recalculate=force_recalculate)

    if wall_coordinates is not None:
        wall_distances, wall_coords_closest, _ = calculate_distances_from_walls(summary, data,
                                                                                wall_summary, wall_data,
                                                                                force_recalculate=force_recalculate)
        mean_wall_dist = np.mean(wall_distances[runi], axis=0)
        min_wall_dist = np.min(wall_distances[runi], axis=0)

        # _, ag_refl_dict = mine_reflection_times(data, summary, wall_summary, wall_data,
        #                                                      ma_window=mov_avg_w, wall_dist_thr=wall_vic_thr,
        #                                                      agent_dist_thr=agent_dist_thr,
        #                                                      turn_thr=turn_thr,
        #                                                      force_recalculate=force_recalculate)

        wall_refl_dict, ag_refl_dict = mine_reflection_times_drotY(data, summary, wall_summary, wall_data)

        wall_reflection_times = []
        for _, v in wall_refl_dict[str(runi)].items():
            wall_reflection_times.extend(v)
        wall_reflection_times = list(set(wall_reflection_times))

        agent_reflection_times = []
        for _, v in ag_refl_dict[str(runi)].items():
            agent_reflection_times.extend(v)
        agent_reflection_times = list(set(agent_reflection_times))

    else:
        mean_wall_dist = min_wall_dist = wall_refl_dict = ag_refl_dict = wall_reflection_times = agent_reflection_times \
            = wall_distances = wall_coords_closest = None

    return com_vel, m_com_vel, abs_vel, m_abs_vel, turning_rates, ma_turning_rates, iidm, min_iidm, mean_iid, pm, ord, mean_pol_vals, \
        mean_wall_dist, min_wall_dist, wall_refl_dict, ag_refl_dict, wall_reflection_times, agent_reflection_times, \
        wall_distances, wall_coords_closest

def butter_lowpass_filter(data, cutoff, fs, order):
    """Butterworth lowpass filter"""
    from scipy.signal import butter, filtfilt
    nyq = 0.5 * fs
    normal_cutoff = cutoff / nyq
    # Get the filter coefficients
    b, a = butter(order, normal_cutoff, btype='low', analog=False)
    y = filtfilt(b, a, data)
    return y


def resampler_2_points(p1, p2, resample_distance, include_endpoint=False):
    """https://stackoverflow.com/questions/64441803/resample-trajectory-to-have-equal-euclidean-distance-in-each-sample"""
    # get the distacne between the points
    distance_p1p2 = np.sqrt(np.sum((p2 - p1) ** 2))

    # check for invalid cases
    if resample_distance > distance_p1p2:
        print("Resample distance larger than distance between points")
        return None

    elif distance_p1p2 == 0:
        print("Distance between the two points is 0")
        return None

    # if all is okay
    else:
        # get the stepsize of x and y coordinates
        stepsize_x, stepsize_y = (p2 - p1) * (resample_distance / distance_p1p2)

        # handle the case when a 'stepsize' along and axis equals 0
        if stepsize_x == 0:
            y = np.arange(p1[1], p2[1], stepsize_y)
            x = np.zeros(len(y)) + p1[0]

        elif stepsize_y == 0:
            x = np.arange(p1[0], p2[0], stepsize_x)
            y = np.zeros(len(x)) + p1[1]

        # all other cases
        else:

            x = np.arange(p1[0], p2[0], stepsize_x)
            y = np.arange(p1[1], p2[1], stepsize_y)

            # optionally append endpoint to final list
        if include_endpoint:
            x = np.append(x, p2[0])
            y = np.append(y, p2[1])

        # retrun the x and y coordinates in two arrays
        return x, y

def resample_wall_coordinates(wall_summary, wall_data, dxy=5, with_plot=False):
    """Resmplaing the wall coordinates to have equal distance between points"""
    # removing any nan values from the wall data
    wall_data = wall_data[:, :, :, ~np.isnan(wall_data[0, 0, 1, :])]
    resampled_wall_coords_x = []
    resampled_wall_coords_y = []
    num_t = wall_data.shape[-1]
    dists = []
    for t in range(num_t-1):
        # taking 2 consecutive points and if they are farther away than dxy, resample
        p1 = np.array([wall_data[0, 0, 1, t], wall_data[0, 0, 3, t]])
        p2 = np.array([wall_data[0, 0, 1, t+1], wall_data[0, 0, 3, t+1]])
        dist = np.sqrt(np.sum((np.array(p2) - np.array(p1))**2))
        dists.append(dist)

        if dist > dxy:
            # if points are too far away we resmple the data
            x, y = resampler_2_points(p1, p2, dxy)
            resampled_wall_coords_x.extend(x)
            resampled_wall_coords_y.extend(y)

        else:
            resampled_wall_coords_x.append(p1[0])
            resampled_wall_coords_y.append(p1[1])

    # creating new data array for the interpolated wall coordinates
    new_shape = wall_data.shape[:-1] + (len(resampled_wall_coords_x), )
    resampled_wall_data = np.zeros(new_shape)
    resampled_wall_data[0, 0, 1, :] = resampled_wall_coords_x
    resampled_wall_data[0, 0, 3, :] = resampled_wall_coords_y

    if with_plot:
        import matplotlib.pyplot as plt
        plt.figure()
        plt.scatter(resampled_wall_data[0, 0, 1, :], resampled_wall_data[0, 0, 3, :], s=1, label='resampled')
        plt.scatter(wall_data[0, 0, 1, :], wall_data[0, 0, 3, :], s=1, c="black", label='original')
        plt.legend()
        plt.xlabel('x [mm]')
        plt.ylabel('y [mm]')
        plt.show()

    return resampled_wall_data


def mine_reflection_times_drotY(data, summary, wall_summary, wall_data,
                                rotation_threshold=0.19, wall_dist_thr=175, agent_dist_thr=175,
                                with_plotting=False, force_recalculate=False):
    """Mining the timepoints when obstacle avoidance was activated. This is
    where a high turning rate is observed together with a small wall-agent,
    obstacle-agent or agent-agent distance. Using the original drotY value coming from OptiTrack"""
    from matplotlib import pyplot as plt
    # collecting basic information about run
    num_t = data.shape[-1]
    num_robots = data.shape[1]
    num_runs = data.shape[0]

    save_path_wa = os.path.join(summary['data_path'], f"{summary['experiment_name']}_warefl.json")
    save_path_aa = os.path.join(summary['data_path'], f"{summary['experiment_name']}_aarefl.json")

    if os.path.isfile(save_path_wa) and os.path.isfile(save_path_aa) and not force_recalculate:
        print("Files in target path for reflections already exists! No recalculation was"
              "requested, so data will be loaded from the json files.")
        with open(save_path_wa, 'r') as fwa:
            wall_refl_dict = json.load(fwa)

        with open(save_path_aa, 'r') as faa:
            ag_refl_dict = json.load(faa)

        return wall_refl_dict, ag_refl_dict

    # calculating wall distances
    wall_distances, wall_coords_closest, _ = calculate_distances_from_walls(summary, data, wall_summary, wall_data)

    # calculating iid distances
    # calculating agent-agent reflections
    iidm = calculate_interindividual_distance(summary, data)
    iidm_nan = iidm.copy()
    for runi in range(num_runs):
        for t in range(num_t):
            np.fill_diagonal(iidm_nan[runi, :, :, t], None)

    wall_refl_dict = {}
    ag_refl_dict = {}

    # framerate
    fs = 30  # Hz
    # sample period
    T = num_t / fs  # s
    cutoff = 0.25  # desired cutoff frequency of the filter, Hz , slightly higher than actual 1.2 Hz
    nyq = 0.5 * fs  # Nyquist Frequency
    order = 5       # sin wave can be approx represented as quadratic
    n = num_t

    # looping through agents, getting their drotY values and thresholding them
    for runi in range(num_runs):
        drotY = data[runi, :, 5, :].copy()
        for ai in range(num_robots):
            # Butterworth low-pass filtering drotY to remove jitters in turning rate
            filtered = butter_lowpass_filter(drotY[ai, :], cutoff, fs, order)

            # # plotting effect of LP filter
            # if with_plotting:
            #     plt.figure()
            #     time = np.linspace(0, T, n, endpoint=False)
            #     plt.plot(time, drotY[ai, :], label='original')
            #     plt.plot(time, filtered, label=f'LP filtered {cutoff}Hz')
            #     plt.legend()
            #     plt.xlabel('Time [s]')
            #     plt.ylabel('drotY [deg/s]')
            #     plt.show()

            drotY[ai, :] = filtered

        # calculating absolute value of derivated drotY to get spikes where the emergency protocol was on
        coll_vec = np.abs(np.diff(drotY, axis=-1))

        # creating nested dictionary with runi and robi as (string) keys
        wall_refl_dict[str(runi)] = dict((str(robi), []) for robi in range(num_robots))
        ag_refl_dict[str(runi)] = dict((str(robi), []) for robi in range(num_robots))

        # looping through agents, getting their drotY values and thresholding them to get time points
        # of emergency protocol
        for ai in range(num_robots):
            # getting thresholded times
            coll_times = np.where(coll_vec[ai, :] > rotation_threshold)[0]

            # calculating wall-agent reflections
            wall_reflection_times = [t for t in coll_times if wall_distances[runi, ai, t] < wall_dist_thr]

            for t in wall_reflection_times:
                ids = np.where(
                    np.logical_and(wall_distances[runi, :, t] < wall_dist_thr, coll_vec[:, t] > rotation_threshold))
                for robi in ids[0]:
                    wall_refl_dict[str(runi)][str(robi)].append(int(t))

            agent_reflection_times = [t for t in coll_times if np.any(iidm_nan[runi, ai, :, t] < agent_dist_thr)]

            for t in agent_reflection_times:
                ids = np.where(
                    np.logical_and(iidm_nan[runi, :, :, t] < agent_dist_thr, coll_vec[ai, t] > rotation_threshold))
                for robi in ids[0]:
                    ag_refl_dict[str(runi)][str(robi)].append(int(t))

            # plotting
            if with_plotting:
                from matplotlib import pyplot as plt
                time = np.linspace(0, T, n, endpoint=False)
                plt.figure(f"a{ai}")
                # plotting normalized rotational values for single robot
                plt.plot(time, (drotY[ai, :] / np.max(drotY[ai, :]))/2 + 1.5, label="normalized drotY")
                # plotting absolute value of derivative
                time = np.linspace(0, T, n - 1, endpoint=False)
                plt.plot(time, coll_vec[ai, :], label="raw derivative")
                # plotting collision times
                plt.plot(np.array(wall_reflection_times)/fs, np.ones(len(wall_reflection_times)), "ro", label="wall")
                plt.plot(np.array(agent_reflection_times)/fs, np.ones(len(agent_reflection_times)), "bo", label="agent")
                # plotting threshold
                plt.hlines(rotation_threshold, 0, T, color="red", linestyle="--", label="peak threshold")
                plt.legend()
                plt.xlabel("time [s]")
                plt.ylabel("drotY")

        if with_plotting:
            plt.show()

        print(f"Saving wall and agent reflection files...")
        with open(save_path_wa, 'w', encoding='utf-8') as fwa:
            json.dump(wall_refl_dict, fwa, ensure_ascii=False, indent=4)

        with open(save_path_aa, 'w', encoding='utf-8') as faa:
            json.dump(ag_refl_dict, faa, ensure_ascii=False, indent=4)

        return wall_refl_dict, ag_refl_dict



def mine_reflection_times(data, summary, wall_summary, wall_data,
                          ma_window=30, wall_dist_thr=200, agent_dist_thr=280, turn_thr=0.0225,
                          force_recalculate=False):
    """Mining the timepoints when obstacle avoidance was activated. This is
    where a high turning rate is observed together with a small wall-agent,
    obstacle-agent or agent-agent distance"""

    print(f"Calculating reflection times for {summary['experiment_name']}"
          f"with window size {ma_window} and thresholds: wall_dist={wall_dist_thr}, "
          f"agent_dist={agent_dist_thr}, turn_rate={turn_thr}")

    # agent turning rates
    turning_rates = calculate_turning_rates(summary, data)
    # moving average of turning rates
    num_t = data.shape[-1]
    num_robots = data.shape[1]
    num_runs = data.shape[0]
    ma_turning_rates = np.zeros_like(turning_rates)

    # wall distances
    wall_distances, wall_coords_closest, _ = calculate_distances_from_walls(summary, data, wall_summary, wall_data)

    # i.i. distances
    iidm = calculate_interindividual_distance(summary, data)
    save_path_wa = os.path.join(summary['data_path'], f"{summary['experiment_name']}_warefl.json")
    save_path_aa = os.path.join(summary['data_path'], f"{summary['experiment_name']}_aarefl.json")

    if os.path.isfile(save_path_wa) and os.path.isfile(save_path_aa) and not force_recalculate:
        print("Files in target path for reflections already exists! No recalculation was"
              "requested, so data will be loaded from the json files.")
        with open(save_path_wa, 'r') as fwa:
            wall_refl_dict = json.load(fwa)

        with open(save_path_aa, 'r') as faa:
            ag_refl_dict = json.load(faa)

    else:
        # min and mean interindividual distances
        iidm_nan = iidm.copy()

        wall_refl_dict = {}
        ag_refl_dict = {}

        for runi in range(num_runs):
            for t in range(num_t):
                np.fill_diagonal(iidm_nan[runi, :, :, t], None)
            wall_refl_dict[str(runi)] = {}
            ag_refl_dict[str(runi)] = {}

            for robi in range(num_robots):
                ma_turning_rates[runi, robi, int(ma_window / 2):-int(ma_window / 2) + 1] = moving_average(
                    turning_rates[runi, robi, :], ma_window)
                wall_refl_dict[str(runi)][str(robi)] = []
                ag_refl_dict[str(runi)][str(robi)] = []

            # calculating wall-agent reflections
            wall_reflection_times = [t for t in range(num_t - 1) if np.any(
                np.logical_and(wall_distances[runi, :, t] < wall_dist_thr, ma_turning_rates[runi, :, t] > turn_thr))]

            for t in wall_reflection_times:
                ids = np.where(
                    np.logical_and(wall_distances[runi, :, t] < wall_dist_thr, ma_turning_rates[runi, :, t] > turn_thr))
                for robi in ids[0]:
                    wall_refl_dict[str(runi)][str(robi)].append(t)

            # calculating agent-agent reflections
            agent_reflection_times = [t for t in range(num_t - 1) if np.any(
                np.logical_and(iidm_nan[runi, :, :, t] < agent_dist_thr, ma_turning_rates[runi, :, t] > turn_thr))]

            for t in agent_reflection_times:
                ids = np.where(
                    np.logical_and(iidm_nan[runi, :, :, t] < agent_dist_thr, ma_turning_rates[runi, :, t] > turn_thr))
                for robi in ids[0]:
                    ag_refl_dict[str(runi)][str(robi)].append(t)

            print(f"Saving wall and agent reflection files...")
            with open(save_path_wa, 'w', encoding='utf-8') as fwa:
                json.dump(wall_refl_dict, fwa, ensure_ascii=False, indent=4)

            with open(save_path_aa, 'w', encoding='utf-8') as faa:
                json.dump(ag_refl_dict, faa, ensure_ascii=False, indent=4)

    return wall_refl_dict, ag_refl_dict


def optitrackcsv_to_VSWRM(csv_path, skip_already_summed=True, dropna=True):
    """Reading an exported optitrack tracking data csv file into a VSWRM summary sata file that can be further used
    for data analysis and plotting with VSWRM

    The exported file must contain 6 columns for each robot, that are
    rotationX, rotationY, rotationZ, positionX, positionY, positionZ

    timepoints where tracking of robots has been lost will be simply cut from the data."""

    data_path = os.path.dirname(csv_path)
    _, csv_filename = os.path.split(csv_path)
    experiment_name = csv_filename.split('.')[0]
    if is_summarized(data_path, experiment_name) and skip_already_summed:
        logger.info(f"Skipping already summed experiment as requested!")
        return True

    df_orig = pd.read_csv(csv_path, skiprows=[i for i in range(6)])

    # QUICKNDIRTY dropping timepoints where optitrack lost track of robots
    if dropna:
        df = df_orig.dropna()
        print("Dropped NA values from dataframe.")
    else:
        df = df_orig

    print("columns: ", df.columns)

    data_holder_columns = [col for col in df.columns if
                           col.startswith('X') or col.startswith('Y') or col.startswith('Z')]
    num_robots = int(len(data_holder_columns) / 6)  # for each robot 3 rotation and 3 position coordinate
    print(f"Found {num_robots} robots data in csv file.")
    time = df['Time (Seconds)'].values
    print(time)
    # time = np.array([a[1] for a in df.index.values[5::]]).astype('float') / 1000
    t_len = len(time)

    attributes = ['t', 'pos_x', 'pos_y', 'pos_z', 'or', 'drotY']
    num_attributes = len(attributes)

    data = np.zeros((1, num_robots, num_attributes, t_len))
    from scipy.spatial.transform import Rotation

    for robi in range(num_robots):
        startindex = int(robi * 6) + 2
        orient_x = df.iloc[:, startindex + 0].values.astype('float')  # x axis
        orient_y = df.iloc[:, startindex + 1].values.astype('float')  # y axis
        orient_z = df.iloc[:, startindex + 2].values.astype('float')  # z axis
        orient = np.array([Rotation.from_euler('xyz', [orient_x[i], orient_y[i], orient_z[i]], degrees=True).as_euler(
            'yxz', degrees=False)[0] for i in range(len(orient_y))])
        orient = - np.pi / 2 - (orient + np.pi)
        x_pos = df.iloc[:, startindex + 3].values.astype('float')
        y_pos = df.iloc[:, startindex + 4].values.astype('float')
        z_pos = df.iloc[:, startindex + 5].values.astype('float')

        drotY = df.iloc[:, startindex + 1].values.astype('float')  # y axis

        data[0, robi, attributes.index('t'), :] = time
        data[0, robi, attributes.index('pos_x'), :] = x_pos
        data[0, robi, attributes.index('pos_y'), :] = y_pos
        data[0, robi, attributes.index('pos_z'), :] = z_pos
        data[0, robi, attributes.index('or'), :] = orient
        data[0, robi, attributes.index('drotY'), :] = drotY

    experiment_summary = {'params': None,
                          'num_runs': 1,
                          'num_robots': num_robots,
                          'num_attributes': num_attributes,
                          'attributes': attributes,
                          'experiment_name': experiment_name}

    with open(os.path.join(data_path, f'{experiment_name}_summaryp.json'), 'w') as sump_f:
        json.dump(experiment_summary, sump_f, indent=4)

    sumd_f = os.path.join(data_path, f'{experiment_name}_summaryd.npy')
    np.save(sumd_f, data)


def summarize_experiment(data_path, experiment_name, skip_already_summed=True):
    """This method summarizes separated WeBots simulation data into a unified satastructure. All measurements must
    have the same length. To accomplish this you should use a positive non-zero PAUSE_SIMULATION_AFTER parameter
    passed from webots.  All robot folders should contain the same number of run folders. Mixed folders resulting from
    changing the number of robots throughout the runs of the experiments will not be handled.

    The output will be saved under data_path in 2 separate files, 1 holding numeric data *_summaryd.npy the other
    the corresponding metadata as a dictinary *_summaryp.json

    The numeric data has the shape of

            (number of runs) x (number of robots) x (number of saved attributes) x (simulation timesteps)

    and the first attribute is always the simulation time"""

    if is_summarized(data_path, experiment_name) and skip_already_summed:
        logger.info(f"Skipping already summed experiment as requested!")
        return True

    attributes = ['t', 'pos_x', 'pos_y', 'pos_z', 'or']
    num_attributes = len(attributes)

    robots = [name for name in os.listdir(data_path) if os.path.isdir(os.path.join(data_path, name))]
    num_robots = len(robots)

    param_dict = {}

    t_len = find_min_t(data_path)

    for i, robot_name in enumerate(robots):
        robot_folder = os.path.join(data_path, robot_name)

        if i == 0:
            runs = [name for name in os.listdir(robot_folder) if os.path.isdir(os.path.join(robot_folder, name))]
            num_runs = len(runs)

        positions = []
        orientations = []
        # t_lens = []
        for j, run_name in enumerate(runs):

            position_array = load_VSWRM_data(
                os.path.join(data_path, robot_name, run_name, f'{robot_name}_run{run_name}_pos.npy'))
            positions.append(position_array)

            or_array = load_VSWRM_data(
                os.path.join(data_path, robot_name, run_name, f'{robot_name}_run{run_name}_or.npy'))
            orientations.append(or_array)

            # t_lens.append(len(position_array[:, 0]))

            with open(os.path.join(data_path, robot_name, run_name,
                                   f'{robot_name}_run{run_name}_params.json')) as param_f:
                if i == 0:
                    param_dict[f'run{run_name}'] = json.load(param_f)
                er_times_path = os.path.join(data_path, robot_name, run_name,
                                             f'{robot_name}_run{run_name}_ERtimes.json')
                if os.path.isfile(er_times_path):
                    with open(er_times_path) as erf:
                        if param_dict[f'run{run_name}'].get('ERtimes') is None:
                            param_dict[f'run{run_name}']['ERtimes'] = {}
                        param_dict[f'run{run_name}']['ERtimes'][robot_name] = json.load(erf)

        if i == 0:
            # t_len = np.min(t_lens)
            t = positions[0][:t_len, 0] / 1000
            data = np.zeros((num_runs, num_robots, num_attributes, t_len))

        for j, run_name in enumerate(runs):
            data[j, i, attributes.index('t'), :] = t
            data[j, i, attributes.index('pos_x'), :] = positions[j][:t_len, 1] * 1000  # in mm
            data[j, i, attributes.index('pos_y'), :] = positions[j][:t_len, 2] * 1000  # in mm
            data[j, i, attributes.index('pos_z'), :] = positions[j][:t_len, 3] * 1000  # in mm
            data[j, i, attributes.index('or'), :] = np.pi / 2 - orientations[j][:t_len, 1]

    experiment_summary = {'params': param_dict,
                          'num_runs': num_runs,
                          'num_robots': num_robots,
                          'num_attributes': num_attributes,
                          'attributes': attributes,
                          'experiment_name': experiment_name}

    with open(os.path.join(data_path, f'{experiment_name}_summaryp.json'), 'w') as sump_f:
        json.dump(experiment_summary, sump_f, indent=4)

    sumd_f = os.path.join(data_path, f'{experiment_name}_summaryd.npy')
    np.save(sumd_f, data)


def read_summary_data(data_path, experiment_name):
    with open(os.path.join(data_path, f'{experiment_name}_summaryp.json')) as sump_f:
        summary_dict = json.load(sump_f)
        summary_dict['data_path'] = data_path

    sumd_f = os.path.join(data_path, f'{experiment_name}_summaryd.npy')
    data = np.load(sumd_f)

    return summary_dict, data


def velocity(position_array, orientation_array, time):
    """position_array shall be of shape (3xN) where N is the number of
    datapoints across time. orientation_array and time shall be N length 1D array"""
    velocities = []

    for t in range(1, position_array.shape[1]):
        if -np.pi / 2 < orientation_array[t] < np.pi / 2:  # aligned with positive z direction
            or_sign = np.sign(position_array[2, t] - position_array[2, t - 1])
        else:
            or_sign = - np.sign(position_array[2, t] - position_array[2, t - 1])
        velocities.append(or_sign * distance(position_array[:, t], position_array[:, t - 1]) / (time[t] - time[t - 1]))

    return np.array([velocities])


def distance(p1, p2):
    """distance of 3D points. Multiple points can be passed in a (3xN) shape
    numpy array where N is the number of points.

    returns an N length 1D array holding the pairwise distances of
    the passed points"""
    squared_dist = np.sum((p1 - p2) ** 2, axis=0)
    dist = np.sqrt(squared_dist)
    return dist


def calculate_velocity(summary, data, force_recalculate=False):
    save_path = os.path.join(summary['data_path'], f"{summary['experiment_name']}_vel.npy")

    if os.path.isfile(save_path) and not force_recalculate:
        print("File in target path for agent velocities already exists! No recalculation was "
              "requested, so data will be loaded from the npy file.")
        velocities = np.load(save_path)
    else:
        velocities = np.zeros((summary['num_runs'], summary['num_robots'], data.shape[-1] - 1))

        t_idx = summary['attributes'].index('t')
        pos_x = summary['attributes'].index('pos_x')
        pos_y = summary['attributes'].index('pos_y')
        pos_z = summary['attributes'].index('pos_z')
        or_idx = summary['attributes'].index('or')

        for i in range(summary['num_runs']):
            for j in range(summary['num_robots']):
                velocities[i, j, :] = velocity(data[i, j, [pos_x, pos_y, pos_z], :],
                                               data[i, j, or_idx, :],
                                               data[i, j, t_idx, :])

        print(f"Saving velocities in npy file under {save_path}")
        np.save(save_path, velocities)

    return velocities


def calculate_distance(summary, data, from_point):
    """Calculating robot ditances from a given point"""

    distances = np.zeros((summary['num_runs'], summary['num_robots'], data.shape[-1]))

    t_idx = summary['attributes'].index('t')
    pos_x = summary['attributes'].index('pos_x')
    pos_y = summary['attributes'].index('pos_y')
    pos_z = summary['attributes'].index('pos_z')

    for i in range(summary['num_runs']):
        for j in range(summary['num_robots']):
            pos_array = data[i, j, [pos_x, pos_y, pos_z], :]
            if i == 0 and j == 0:
                reference = np.zeros_like(pos_array)
                reference[0, :] = from_point[0]
                reference[1, :] = from_point[1]
                reference[2, :] = from_point[2]

            distances[i, j, :] = distance(pos_array, reference)

    return distances


def calculate_avg_metric_after_wall_refl(metric_m, wall_refl, time_windows):
    """Calculating teh typical metric shape (as in mean metric) after reflection times with
    standard deviation. time windows is a list of window before, and after"""
    snips = None
    num_t = metric_m.shape[-1]
    num_snips = 0
    for t in wall_refl:
        if t + time_windows[1] < num_t and t - time_windows[0] > 0:
            snippet = metric_m[t - time_windows[0]:t + time_windows[1]]
            # snippet = (snippet - np.min(snippet)) / (np.max(snippet) - np.min(snippet))
            if snips is None:
                snips = snippet
            else:
                snips = np.vstack((snips, snippet))
    return np.mean(snips, axis=0), np.std(snips, axis=0)


def calculate_interindividual_distance(summary, data, force_recalculate=False):
    save_path = os.path.join(summary['data_path'], f"{summary['experiment_name']}_iid.npy")

    if os.path.isfile(save_path) and not force_recalculate:
        print("File in target path for IID already exists! No recalculation was "
              "requested, so data will be loaded from the npy file.")
        iid = np.load(save_path)

    else:
        iid = np.zeros((summary['num_runs'], summary['num_robots'], summary['num_robots'], data.shape[-1]))

        t_idx = summary['attributes'].index('t')
        pos_x = summary['attributes'].index('pos_x')
        pos_y = summary['attributes'].index('pos_y')
        pos_z = summary['attributes'].index('pos_z')

        for runi in range(summary['num_runs']):
            for robi in range(summary['num_robots']):
                for robj in range(summary['num_robots']):
                    pos_array_i = data[runi, robi, [pos_x, pos_y, pos_z], :]
                    pos_array_j = data[runi, robj, [pos_x, pos_y, pos_z], :]
                    iid[runi, robi, robj, :] = distance(pos_array_i, pos_array_j)

        print(f"Saving IID matrix in npy file under {save_path}")
        np.save(save_path, iid)

    return iid


def calculate_mean_iid(summary, data, window_width=100):
    iid = calculate_interindividual_distance(summary, data)
    for i in range(summary['num_robots']):
        iid[:, i, i, :] = np.inf

    miid = np.zeros((summary['num_runs'],))

    for i in range(summary['num_runs']):
        miid[i] = np.mean(np.min(np.min(iid[i, :, :, -window_width::], axis=0), axis=0))

    return miid


def calculate_reflection_times(summary, data):
    pass


def seriation(Z, N, cur_index):
    '''
        input:
            - Z is a hierarchical tree (dendrogram)
            - N is the number of points given to the clustering process
            - cur_index is the position in the tree for the recursive traversal
        output:
            - order implied by the hierarchical tree Z

        seriation computes the order implied by a hierarchical tree (dendrogram)
    '''
    if cur_index < N:
        return [cur_index]
    else:
        left = int(Z[cur_index - N, 0])
        right = int(Z[cur_index - N, 1])
        return (seriation(Z, N, left) + seriation(Z, N, right))


def compute_serial_matrix(dist_mat, method="ward"):
    '''
        input:
            - dist_mat is a distance matrix
            - method = ["ward","single","average","complete"]
        output:
            - seriated_dist is the input dist_mat,
              but with re-ordered rows and columns
              according to the seriation, i.e. the
              order implied by the hierarchical tree
            - res_order is the order implied by
              the hierarhical tree
            - res_linkage is the hierarhical tree (dendrogram)

        compute_serial_matrix transforms a distance matrix into
        a sorted distance matrix according to the order implied
        by the hierarchical tree (dendrogram)
    '''
    N = len(dist_mat)
    flat_dist_mat = squareform(dist_mat)
    res_linkage = linkage(flat_dist_mat, method=method, preserve_input=True)
    res_order = seriation(res_linkage, N, N + N - 2)
    seriated_dist = np.zeros((N, N))
    a, b = np.triu_indices(N, k=1)
    seriated_dist[a, b] = dist_mat[[res_order[i] for i in a], [res_order[j] for j in b]]
    seriated_dist[b, a] = seriated_dist[a, b]

    return seriated_dist, res_order, res_linkage


def subgroup_clustering(summary, pm, iidm, valid_ts, runi=0, force_recalculate=False):
    """Using hierarhical clustering according to iid and pm scores to get number of subgroups"""
    if isinstance(valid_ts, list):
        valid_ts = np.array(valid_ts)
    save_path = os.path.join(summary['data_path'], f"{summary['experiment_name']}_run{runi}_clustering.json")
    if os.path.isfile(save_path) and not force_recalculate:
        print("Files in target path for clustering already exists! No recalculation was"
              "requested, so data will be loaded from the json files.")
        with open(save_path, 'r') as fwa:
            clustering_dict = json.load(fwa)
        loaded_validts = clustering_dict['valid_ts']
        loaded_validts = [int(t) for t in loaded_validts]
        if loaded_validts != valid_ts.tolist():
            print(
                "Loaded json file for clustering but valid timepoints don't match with the one requested. Recalculating!")
            clustering_dict = subgroup_clustering(summary, pm, iidm, valid_ts, runi=0, force_recalculate=True)
            return clustering_dict
        else:
            return clustering_dict
    else:
        clustering_dict = {}
        num_robots = pm.shape[1]
        clustering_dict['num_subgroups'] = []
        clustering_dict['valid_ts'] = valid_ts.tolist()
        for t in valid_ts:
            niidm = (iidm[runi, :, :, t] - np.min(iidm[runi, :, :, t])) / (
                    np.max(iidm[runi, :, :, t]) - np.min(iidm[runi, :, :, t]))
            dist = (1 - pm[runi, :, :, t].astype('float') + niidm) / 2
            # sermat = compute_serial_matrix(1-pm[0, :, :, t].astype('float'))
            linkage_matrix = linkage(dist, "single")
            ret = dendrogram(linkage_matrix, color_threshold=1.2, labels=[i for i in range(num_robots)],
                             show_leaf_counts=True, no_plot=True)
            clustering_dict['num_subgroups'].append(len(list(set(ret['color_list']))))

        print(f"Saving clustering data...")
        print(clustering_dict)
        with open(save_path, 'w', encoding='utf-8') as fwa:
            json.dump(clustering_dict, fwa, ensure_ascii=False, indent=4)

        return clustering_dict


def calculate_order_parameter(summary, data, force_recalculate=False):
    """Calculating matrix including order parameter values between 0 and 1 reflecting the
    average match of the agents w.r.t. heaing angles, as the norm of the summed unit
    vectors with direction of agent orientations"""
    save_path = os.path.join(summary['data_path'], f"{summary['experiment_name']}_ord.npy")

    if os.path.isfile(save_path) and not force_recalculate:
        print("File in target path for order parameters already exists! No recalculation was "
              "requested, so data will be loaded from the npy file.")
        ord = np.load(save_path)

    else:
        or_idx = summary['attributes'].index('or')
        orientations = data[:, :, or_idx, :]

        unitvecs = np.zeros((summary['num_runs'], 2, summary['num_robots'], data.shape[-1]))

        for runi in range(summary['num_runs']):
            for robi in range(summary['num_robots']):
                ori = orientations[runi, robi, :]
                unitvecs[runi, 0, robi, :] = np.array([np.cos(ang) for ang in ori])
                unitvecs[runi, 1, robi, :] = np.array([np.sin(ang) for ang in ori])

        unitsum = np.sum(unitvecs, axis=2)
        ord = np.zeros((summary['num_runs'], data.shape[-1]))
        for runi in range(summary['num_runs']):
            ord[runi, :] = np.array(
                [np.linalg.norm([unitsum[runi, 0, t], unitsum[runi, 1, t]]) / summary['num_robots'] for t in
                 range(data.shape[-1])])

        print(f"Saving order parameter matrix in npy file under {save_path}")
        np.save(save_path, ord)

    return ord


def calculate_ploarization_matrix(summary, data, force_recalculate=False):
    """Calculating matrix including polariuzation values between 0 and 1 reflecting the
    average match of the agents w.r.t. heaing angles"""
    save_path = os.path.join(summary['data_path'], f"{summary['experiment_name']}_pol.npy")

    if os.path.isfile(save_path) and not force_recalculate:
        print("File in target path for polarization already exists! No recalculation was "
              "requested, so data will be loaded from the npy file.")
        pol = np.load(save_path)

    else:
        time = data[0, 0, 0, :]  # 1 step shorter because of diff

        or_idx = summary['attributes'].index('or')
        orientations = data[:, :, or_idx, :]

        pol = np.zeros((summary['num_runs'], summary['num_robots'], summary['num_robots'], data.shape[-1]))

        for i in range(summary['num_robots']):
            pol[:, i, i, :] = np.nan

        for i in range(summary['num_runs']):
            for ri in range(summary['num_robots']):
                for rj in range(summary['num_robots']):
                    diff = np.abs(orientations[i, ri, :] - orientations[i, rj, :])
                    pol[i, ri, rj, :] = ((2 / np.pi) * np.abs(diff - np.pi)) - 1

        print(f"Saving polarization matrix in npy file under {save_path}")
        np.save(save_path, pol)

    return pol


def calculate_mean_polarization(summary, data, window_width=100):
    pol = calculate_ploarization_matrix(summary, data)
    mean_pol = np.zeros(summary['num_runs'])

    for i in range(summary['num_runs']):
        p_vec = np.zeros(window_width)
        norm_fac = 0
        for ri in range(summary['num_robots']):
            for rj in range(ri, summary['num_robots']):
                p_vec += pol[i, ri, rj, -window_width::]
                norm_fac += 1
        mean_pol[i] = np.mean((p_vec / (norm_fac)))

    return mean_pol


def calculate_min_iid(summary, data):
    iid = calculate_interindividual_distance(summary, data)
    for i in range(summary['num_robots']):
        iid[:, i, i, :] = np.inf

    min_iid = np.zeros((summary['num_runs'],))

    for i in range(summary['num_runs']):
        min_iid[i] = np.min(iid[i, :, :, :])

    return min_iid


def population_velocity(summary, data, force_recalculate=False):
    """Calculating the velocity and direction of velocity of the center of mass of agents

    returns an (n_runs x (t-1)) shape matrix"""

    save_path = os.path.join(summary['data_path'], f"{summary['experiment_name']}_COMvel.npy")

    if os.path.isfile(save_path) and not force_recalculate:
        print("File in target path for COM velocity already exists! No recalculation was"
              "requested, so data will be loaded from the npy file.")
        COMvelocity = np.load(save_path)

    else:
        pos_x = summary['attributes'].index('pos_x')
        pos_y = summary['attributes'].index('pos_y')
        pos_z = summary['attributes'].index('pos_z')
        center_of_mass = np.mean(data[:, :, [pos_x, pos_y, pos_z], :], axis=1)

        t = data[0, 0, 0, :]
        dt = t[1] - t[0]

        COMvelocity = np.zeros((summary['num_runs'], len(t) - 1))
        for i in range(len(t) - 1):
            for run_i in range(summary['num_runs']):
                COMvelocity[run_i, i] = distance(center_of_mass[run_i, :, i], center_of_mass[run_i, :, i + 1]) / dt

        print(f"Saving COMvelcoity in npy file under {save_path}")
        np.save(save_path, COMvelocity)

    return COMvelocity


def get_collision_time_intervals(summary):
    """Calculating larger collision time intervals for all robot and run according to raw recorded ERtimes
    in summary data"""

    collision_intervals = dict.fromkeys(range(0, summary['num_runs']),
                                        dict.fromkeys(range(0, summary['num_robots']), []))
    collision_times = {}
    for i in range(summary['num_runs']):
        collision_times[i] = {}
        for j in range(summary['num_robots']):
            r_sum = summary['params'][f'run{i + 1}']
            col_times_dict = r_sum.get("ERtimes")
            if col_times_dict is not None:
                rob_col_times = col_times_dict.get(f'robot{j}')
                if rob_col_times is not None:
                    collision_times[i][j] = np.array(rob_col_times.get("ERtimes"))

    # from pprint import pprint
    # r_i = 0
    # for run_name, r_sum in summary['params'].items():
    #     col_times_dict = r_sum.get("ERtimes")  # all collisions in run
    #
    #     if col_times_dict is not None:  # there were collisions during the experiment, looping through robots
    #         for robi in range(summary['num_robots']):
    #             rob_col_times = col_times_dict.get(f'robot{robi}')
    #             if rob_col_times is not None:
    #                 ctimes = np.array(rob_col_times.get("ERtimes"))
    #                 # print(ctimes / 1000)
    #                 collision_times[r_i][robi] = ctimes.copy()
    #                 # print(collision_times[r_i][robi])
    #
    #                 # time difference between 2 ER reports is larger than reporting frequency
    #                 mask_temp = list(np.where(np.diff(ctimes) > 300)[0])
    #                 mask = [0]  # first element always border
    #                 mask.append(len(ctimes) - 1)  # last element always border
    #                 mask.extend(mask_temp)
    #
    #                 mask_temp = list(np.where(np.diff(ctimes) > 300)[0] + 1)  # get end of intervals
    #                 mask.extend(mask_temp)
    #                 collision_intervals[r_i][robi] = np.array(sorted(list(ctimes[mask])))
    #
    #     r_i += 1

    return collision_intervals, collision_times


def get_collisions_with_type(summary, data, range_around_col=0.3, BL_thr=0.18):
    """Returns the collision data from the experiments including the type of the collisions, that can be
    robot-robot vs robot-static. The former one is defined according to any robot-robot distance did go below
    BL_thr within <range_around_col> timerange (in sec) from the timepoint of collision"""
    t = data[0, 0, 0, :]
    dt = t[1] - t[0]
    range_steps = int(range_around_col / dt)

    coll_intervals, coll_times = get_collision_time_intervals(summary)

    iid_matrix = calculate_interindividual_distance(summary, data)
    for i in range(summary['num_runs']):
        for j in range(summary['num_robots']):
            iid_matrix[i, j, j, :] = np.inf

    collision_times = dict.fromkeys(range(0, summary['num_runs']),
                                    dict.fromkeys(range(0, summary['num_robots']), []))

    for i in range(summary['num_runs']):
        if i in coll_times.keys():
            for j in range(summary['num_robots']):
                if j in coll_times[i].keys():
                    for ct in sorted(coll_times[i][j]):
                        t_start, = np.where(np.isclose(t, ct / 1000))
                        if len(t_start) == 0:
                            print(i, j, 'WARNING: skip step with ', ct / 1000)
                            continue
                        t_start = int(t_start)
                        t_end = np.min([t_start + range_steps, len(t) - 1])
                        t_end = int(t_end)
                        if np.min(iid_matrix[i, j, :, t_start:t_end]) < BL_thr:
                            # print('COLLISION WITH ROBOT')
                            # print(f"run{i}@{ct / 1000}")
                            # print(np.min(iid_matrix[i, j, :, t_start:t_end]))
                            collision_times[i][j].append((ct, 'withRobot'))
                        else:
                            collision_times[i][j].append((ct, 'withStatic'))

    return collision_times


def get_robot_collision_ratio(summary, data):
    """Returns the ratio of robot-robot vs robot-static collisions throughout the whole experiment with all runs"""
    collisions = get_collisions_with_type(summary, data)
    with_robot = []
    with_static = []
    for i in range(summary['num_runs']):
        for j in range(summary['num_robots']):
            with_robot.extend([elem[0] for elem in collisions[i][j] if elem[1] == 'withRobot'])
            with_static.extend([elem[0] for elem in collisions[i][j] if elem[1] == 'withStatic'])
    if (len(with_static) + len(with_robot)) > 0:
        return len(with_robot) / (len(with_static) + len(with_robot)), len(with_robot), len(with_static)
    else:
        return 0, len(with_robot), len(with_static)


def moving_average(x, N, weights=1):
    """Simple moving average with window length N"""
    return np.convolve(x, np.ones(N) * weights, 'valid') / N


def is_crystallized(summary, data, num_run, time_window=5, vel_thr=0.003, polvar_thr=0.3, pol_thr=0.5):
    """Returns a bool showing if a given run in an experiment was stuck in a crystallized state according to the
    population velocity and the variance of the polarization of agents in the last <time_window> seconds"""

    t = data[0, 0, 0, :-1]
    dt = t[1] - t[0]
    num_timesteps = int(time_window / dt)

    COM_vel = population_velocity(summary, data)[num_run, :]
    polarization = calculate_ploarization_matrix(summary, data)
    population_mean = np.mean(np.mean(polarization, 1), 1)[num_run, :]

    # print('mean COM velocity: ', np.mean(COM_vel[-num_timesteps:]))
    # print('std polarization: ', np.std(population_mean[-num_timesteps:]))
    # print('men polarization: ', np.mean(population_mean[-num_timesteps:]))
    if np.mean(COM_vel[-num_timesteps:]) < vel_thr and np.std(population_mean[-num_timesteps:]) < polvar_thr \
            and np.mean(population_mean[-num_timesteps:]) < pol_thr:
        return True
    else:
        return False


def return_mean_polarization_at_end(summary, data, time_window=5):
    """Returns the mean polarization in the last <time_window> seconds of the experiment"""
    t = data[0, 0, 0, :-1]
    dt = t[1] - t[0]
    num_timesteps = int(time_window / dt)

    polarization = calculate_ploarization_matrix(summary, data)
    population_mean = np.mean(np.mean(np.mean(polarization, 1), 1), axis=0)[-num_timesteps:]
    return np.mean(population_mean)


def t_without_collision(summary, data, collision_removal_range=1):
    """Returning a time axis for each run from which timepoints are excluded
     where at least one of the agents collided"""

    t = data[0, 0, 0, :]
    dt = t[1] - t[0]
    num_timesteps = int(collision_removal_range / dt)

    ctimes = get_collisions_with_type(summary, data)
    filtered_ts = {}
    for i in range(summary['num_runs']):
        filtered_ts[i] = None

    for i in range(summary['num_runs']):
        if i in ctimes.keys():
            filtered_t = t.copy()
            for j in range(summary['num_robots']):
                if j in ctimes[i].keys():
                    for ct in ctimes[i][j]:
                        t_start, = np.where(np.isclose(t, ct[0] / 1000))
                        if len(t_start) == 0:
                            print(i, j, 'WARNING: skip step with ', ct[0] / 1000)
                            continue
                        t_start = int(t_start)
                        t_end = np.min([t_start + num_timesteps, len(t) - 1])
                        t_end = int(t_end)
                        filtered_t[t_start:t_end] = -1
            filtered_t2 = filtered_t.copy()
            filtered_t2 = filtered_t2[filtered_t2 > -1]
            filtered_ts[i] = filtered_t2
        else:
            filtered_ts[i] = t.copy()

    return filtered_ts


def return_mean_polarization_without_collision(summary, data, collision_removal_range=1):
    """Calculating the mean polarization of the group in those times when none of the robots collided"""
    noc_t = t_without_collision(summary, data, collision_removal_range=collision_removal_range)
    pol_matrix = calculate_ploarization_matrix(summary, data)

    t = data[0, 0, 0, :]

    run_means = []

    for i in range(summary['num_runs']):
        if i in noc_t.keys():
            _, _, x_ind = np.intersect1d(t, noc_t[i] / 1000, return_indices=True)
            polarization = pol_matrix[i, :, :, x_ind]
            run_means.append(np.mean(np.mean(np.mean(polarization, 1), 1)))

    return np.mean(run_means)


def time_spent_undesired_state(summary, data, vel_thr=0.02):
    """calculating time spent in an undesired state where the average COM velocity is zero of the group."""
    t = data[0, 0, 0, :-1]
    dt = t[1] - t[0]

    COM_vel = population_velocity(summary, data)

    times = []

    for i in range(summary['num_runs']):
        undes_state_mask = COM_vel[i, :] < vel_thr
        times.append(len(t[undes_state_mask]))

    return (np.mean(times) * dt) / t[-1]


def overall_COM_travelled_distance_while_polarized(summary, data, polarization_thr=0.7):
    """Calculating the overall travelled distance of the COM of the group while the group was polarized.
    This excludes those undesired states when the group is unpolarized but still pulling andpushing each other
    away and by that moving the COMN of the group."""
    t = data[0, 0, 0, :-1]
    dt = t[1] - t[0]

    polarization = calculate_ploarization_matrix(summary, data)
    mean_polarization = np.mean(np.mean(polarization, 1), 1)
    travelled_distances = []
    for i in range(summary['num_runs']):
        high_pol_mask = mean_polarization[i, :-1] > polarization_thr
        pop_distance = population_velocity(summary, data) * dt
        travelled_distances.append(np.sum(pop_distance[i, high_pol_mask]))

    return np.sum(travelled_distances)
