#!/usr/bin/env python3
"""
自主2D空间感知灯语集群实验 - 智能体版本
完全自主的智能体，实现连续环境感知与智能决策

核心特性:
- 4摄像头360°持续环境扫描
- 智能早停机制(2秒无目标/5秒有目标)
- 实时灯语分析与空间计算
- 智能目标选择与立即执行
- 8秒指令冷却防重复机制
- 0.2秒循环间隔持续监控
"""

import sys
import os

# 首先设置日志抑制
from suppress_logs import setup_log_suppression
setup_log_suppression()

# 添加swarm_experiment模块到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'swarm_experiment'))

from swarm_experiment.core.experiment_controller import ExperimentController

def main():
    """主函数 - 直接启动自主模式"""
    try:
        print("🤖 启动自主集群智能体系统...")
        print("📡 4摄像头360°持续扫描 | ⚡智能早停 | 🔄8秒冷却")
        
        controller = ExperimentController()
        
        # 直接启动自主模式
        controller.start_autonomous_mode()
        
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
