#!/usr/bin/env python3
"""
重构后的简化2D空间感知灯语集群实验 - 主程序
使用模块化架构，代码更清晰、更易维护

原文件：V6simple_2d_swarm_experiment.py (1243行)
重构后：模块化架构，主程序仅30行
"""

import sys
import os

# 首先设置日志抑制
from suppress_logs import setup_log_suppression
setup_log_suppression()

#可能要先这句话
#source /opt/ros/noetic/setup.bash

# 添加swarm_experiment模块到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'swarm_experiment'))

from swarm_experiment.core.experiment_controller import ExperimentController

def main():
    """主函数"""
    try:
        print("🚀 启动重构版集群实验系统...")
        controller = ExperimentController()
        controller.run_experiment()
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
