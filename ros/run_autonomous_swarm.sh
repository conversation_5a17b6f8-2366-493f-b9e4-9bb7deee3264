#!/bin/bash
# 自主集群实验启动脚本
# 启动完全自主的智能体模式，实现连续环境感知与智能决策

echo "🤖 启动自主集群智能体系统..."

# 设置ROS环境
echo "📡 设置ROS环境..."
source /opt/ros/noetic/setup.bash

# 抑制各种日志输出
echo "🔇 配置日志抑制..."
export TF_CPP_MIN_LOG_LEVEL=3
export CUDA_LAUNCH_BLOCKING=0
export PYTHONWARNINGS="ignore"
export YOLO_VERBOSE=False

# 检查ROS环境
echo "🔍 检查ROS环境..."
python3 -c "import rospy; print('✅ ROS环境正常')" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ ROS环境检查通过"
else
    echo "❌ ROS环境检查失败"
    echo "请确保ROS Noetic已正确安装"
    exit 1
fi

# 显示自主模式特性
echo ""
echo "🚀 自主模式核心特性:"
echo "  📡 4摄像头360°持续环境扫描"
echo "  ⚡ 智能早停机制(2秒无目标/5秒有目标)"
echo "  🧠 实时灯语分析与空间计算"
echo "  🎯 智能目标选择与立即执行"
echo "  ❄️ 8秒指令冷却防重复机制"
echo "  🔄 0.2秒循环间隔持续监控"
echo ""

# 启动自主版本
echo "🎯 启动自主集群智能体..."
python3 V6autonomous_2d_swarm_experiment.py

echo "🏁 自主智能体结束"
