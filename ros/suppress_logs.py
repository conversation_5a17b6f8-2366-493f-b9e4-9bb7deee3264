#!/usr/bin/env python3
"""
日志抑制配置脚本
在主程序启动前设置各种日志抑制选项
"""

import os
import sys
import warnings
import logging

def setup_log_suppression():
    """设置全面的日志抑制"""
    
    # 1. Python警告抑制
    warnings.filterwarnings("ignore")
    
    # 2. 环境变量设置
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # TensorFlow
    os.environ['CUDA_LAUNCH_BLOCKING'] = '0'  # CUDA
    os.environ['PYTHONWARNINGS'] = 'ignore'   # Python警告
    os.environ['YOLO_VERBOSE'] = 'False'      # YOLO
    
    # 3. 各种库的日志级别设置
    logging.getLogger("ultralytics").setLevel(logging.ERROR)
    logging.getLogger("torch").setLevel(logging.ERROR)
    logging.getLogger("torchvision").setLevel(logging.ERROR)
    logging.getLogger("PIL").setLevel(logging.ERROR)
    logging.getLogger("matplotlib").setLevel(logging.ERROR)
    
    # 4. 尝试抑制TensorRT日志（如果可用）
    try:
        import tensorrt as trt
        # 设置TensorRT日志级别
        trt_logger = trt.Logger(trt.Logger.ERROR)
    except ImportError:
        pass
    
    # 5. 抑制CUDA相关警告
    try:
        import torch
        torch.cuda.set_device(0)  # 设置默认GPU
    except:
        pass
    
    print("🔇 日志抑制配置完成")
    print("   - Python警告: 已抑制")
    print("   - TensorFlow日志: 已抑制")
    print("   - YOLO输出: 已抑制")
    print("   - TensorRT日志: 已抑制")

if __name__ == "__main__":
    setup_log_suppression()
