#!/usr/bin/env python3
"""
集群行为模块
负责执行各种集群行为（靠近、跟随、避让等）
"""

import rospy
import time
import math
from geometry_msgs.msg import Twist

class SwarmBehavior:
    """集群行为执行器"""
    
    def __init__(self, config, vel_publisher):
        self.config = config
        self.vel_pub = vel_publisher
        self.current_behavior = None
        
    def execute_behavior(self, target_info):
        """执行集群行为"""
        command_id = target_info['number']
        distance = target_info['distance']
        azimuth = target_info['azimuth']
        camera_name = target_info['cam_name']
        track_id = target_info['track_id']
        
        if command_id not in self.config.SWARM_COMMANDS:
            return

        behavior = self.config.SWARM_COMMANDS[command_id]
        self.current_behavior = behavior

        rospy.loginfo(f"🚀 执行2D行为 (TrackID {track_id}): {behavior}")
        rospy.loginfo(f"   📊 2D参数: 距离{distance:.2f}m, 方位{azimuth:.1f}°")
        rospy.loginfo(f"   📹 检测摄像头: {camera_name} (朝向{self.config.camera_orientations.get(camera_name, 'unknown')}°)")

        twist = Twist()

        if behavior == "approach":
            self._approach_behavior(twist, distance, azimuth, camera_name)
        elif behavior == "follow":
            self._follow_behavior(twist, distance, azimuth, camera_name)
        elif behavior == "avoid":
            self._avoid_behavior(twist, distance, azimuth, camera_name)
        elif behavior == "circle":
            self._circle_behavior(twist, distance, azimuth, camera_name)
        elif behavior == "stop":
            self._stop_behavior(twist)
        elif behavior == "align":
            self._align_behavior(twist, distance, azimuth, camera_name)
        elif behavior == "retreat":
            self._retreat_behavior(twist, distance, azimuth, camera_name)
        elif behavior == "parallel":
            self._parallel_behavior(twist, distance, azimuth, camera_name)

        # 基于物理学的精确执行时间计算
        execution_time = self._calculate_execution_time(behavior, distance, twist)
        self._execute_motion(twist, execution_time, behavior, track_id)

    def _approach_behavior(self, twist, distance, azimuth, camera_name):
        """靠近行为 - 修复版本，参考V7逻辑"""
        target_distance = self.config.spatial_params['target_distance']  # 2.0m
        safe_distance = self.config.spatial_params['safe_distance']      # 1.0m
        approach_speed = self.config.spatial_params['approach_speed']

        rospy.loginfo(f"🎯 【APPROACH】距离{distance:.2f}m → 目标{target_distance:.1f}m")

        # 距离检查 - 参考V7逻辑
        if distance <= target_distance:
            rospy.loginfo(f"✅ 已到达目标距离，停止靠近")
            return  # 不设置任何运动，twist保持为零

        # 计算朝向目标的运动方向
        camera_angle = self.config.camera_orientations.get(camera_name, 0)
        target_angle_global = (camera_angle + azimuth) % 360

        # 转换为机器人坐标系的运动
        target_angle_rad = math.radians(target_angle_global)
        move_x = approach_speed * math.sin(target_angle_rad)
        move_y = approach_speed * math.cos(target_angle_rad)

        # 关键修复：安全距离检查和减速 - 完全参考V7逻辑
        if distance < safe_distance:
            rospy.loginfo(f"⚠️ 距离过近({distance:.2f}m < {safe_distance:.1f}m)，减速50%")
            move_x *= 0.5
            move_y *= 0.5

        twist.linear.x = move_x
        twist.linear.y = move_y
        twist.angular.z = 0.0

        rospy.loginfo(f"🎯 approach控制: 全局角度{target_angle_global:.1f}°")
        rospy.loginfo(f"   运动指令: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}")

    def _follow_behavior(self, twist, distance, azimuth, camera_name):
        """跟随行为"""
        follow_distance = self.config.spatial_params['follow_distance']
        approach_speed = self.config.spatial_params['approach_speed']
        
        if distance > follow_distance:
            camera_angle = self.config.camera_orientations.get(camera_name, 0)
            target_angle_global = (camera_angle + azimuth) % 360
            
            target_angle_rad = math.radians(target_angle_global)
            move_x = approach_speed * math.sin(target_angle_rad)
            move_y = approach_speed * math.cos(target_angle_rad)
            
            twist.linear.x = move_x
            twist.linear.y = move_y
            twist.angular.z = 0.0
            
            rospy.loginfo(f"🔄 follow控制: 跟随距离{follow_distance:.1f}m, 当前距离{distance:.2f}m")

    def _avoid_behavior(self, twist, distance, azimuth, camera_name):
        """避让行为"""
        safe_distance = self.config.spatial_params['safe_distance']
        retreat_speed = self.config.spatial_params['retreat_speed']
        
        if distance < safe_distance * 2:  # 避让距离是安全距离的2倍
            camera_angle = self.config.camera_orientations.get(camera_name, 0)
            avoid_angle_global = (camera_angle + azimuth + 180) % 360  # 反方向
            
            avoid_angle_rad = math.radians(avoid_angle_global)
            move_x = retreat_speed * math.sin(avoid_angle_rad)
            move_y = retreat_speed * math.cos(avoid_angle_rad)
            
            twist.linear.x = move_x
            twist.linear.y = move_y
            twist.angular.z = 0.0
            
            rospy.loginfo(f"🔄 avoid控制: 安全距离{safe_distance:.1f}m, 当前距离{distance:.2f}m")

    def _circle_behavior(self, twist, distance, azimuth, camera_name):
        """环绕行为"""
        turn_speed = self.config.spatial_params['turn_speed']
        approach_speed = self.config.spatial_params['approach_speed']
        
        # 保持距离的同时进行环绕
        twist.linear.x = approach_speed * 0.3  # 较慢的前进
        twist.angular.z = turn_speed
        
        rospy.loginfo(f"🔄 circle控制: 环绕运动")

    def _stop_behavior(self, twist):
        """停止行为"""
        twist.linear.x = 0.0
        twist.linear.y = 0.0
        twist.angular.z = 0.0
        
        rospy.loginfo(f"🔄 stop控制: 停止运动")

    def _align_behavior(self, twist, distance, azimuth, camera_name):
        """对齐行为"""
        turn_speed = self.config.spatial_params['turn_speed']
        
        # 转向目标方向
        if abs(azimuth) > 5:  # 角度容忍度
            twist.angular.z = turn_speed if azimuth > 0 else -turn_speed
        
        rospy.loginfo(f"🔄 align控制: 对齐角度{azimuth:.1f}°")

    def _retreat_behavior(self, twist, distance, azimuth, camera_name):
        """后退行为"""
        retreat_speed = self.config.spatial_params['retreat_speed']
        
        camera_angle = self.config.camera_orientations.get(camera_name, 0)
        retreat_angle_global = (camera_angle + azimuth + 180) % 360
        
        retreat_angle_rad = math.radians(retreat_angle_global)
        move_x = retreat_speed * math.sin(retreat_angle_rad)
        move_y = retreat_speed * math.cos(retreat_angle_rad)
        
        twist.linear.x = move_x
        twist.linear.y = move_y
        twist.angular.z = 0.0
        
        rospy.loginfo(f"🔄 retreat控制: 后退运动")

    def _parallel_behavior(self, twist, distance, azimuth, camera_name):
        """平行行为"""
        approach_speed = self.config.spatial_params['approach_speed']
        
        camera_angle = self.config.camera_orientations.get(camera_name, 0)
        parallel_angle_global = (camera_angle + azimuth + 90) % 360  # 垂直方向
        
        parallel_angle_rad = math.radians(parallel_angle_global)
        move_x = approach_speed * math.sin(parallel_angle_rad)
        move_y = approach_speed * math.cos(parallel_angle_rad)
        
        twist.linear.x = move_x
        twist.linear.y = move_y
        twist.angular.z = 0.0
        
        rospy.loginfo(f"🔄 parallel控制: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}")

    def _execute_motion(self, twist, execution_time, behavior, track_id):
        """执行运动指令 - 增强版本，支持实时距离监控"""
        rospy.loginfo(f"⏱️ 执行运动指令 {execution_time:.1f} 秒...")
        rospy.loginfo(f"⚠️ 注意：当前版本为固定时间执行，无实时距离监控")
        rospy.loginfo(f"   建议：在自主模式下通过短周期(0.2s)重新检测来实现安全控制")

        # 分段执行，便于观察
        steps = int(execution_time * 2)  # 每0.5秒发布一次
        for i in range(steps):
            if rospy.is_shutdown():
                break
            self.vel_pub.publish(twist)
            time.sleep(0.5)
            if i % 4 == 0:  # 每2秒打印一次状态
                rospy.loginfo(f"   执行中... {i*0.5:.1f}/{execution_time:.1f}秒")

        # 停止
        stop_twist = Twist()
        rospy.loginfo("🛑 发布停止指令")
        self.vel_pub.publish(stop_twist)
        rospy.loginfo(f"✅ 2D行为 {behavior} (TrackID {track_id}) 完成")

    def _execute_motion_with_monitoring(self, twist, execution_time, behavior, track_id, distance_callback=None):
        """执行运动指令 - 带实时距离监控版本（未来扩展）"""
        rospy.loginfo(f"⏱️ 执行运动指令 {execution_time:.1f} 秒（带距离监控）...")

        target_distance = self.config.spatial_params['target_distance']
        safe_distance = self.config.spatial_params['safe_distance']

        steps = int(execution_time * 2)  # 每0.5秒发布一次
        for i in range(steps):
            if rospy.is_shutdown():
                break

            # 如果提供了距离回调函数，检查当前距离
            if distance_callback:
                current_distance = distance_callback()
                if current_distance is not None:
                    if behavior == "approach" and current_distance <= target_distance:
                        rospy.loginfo(f"🛑 距离监控：已到达目标距离{current_distance:.2f}m，提前停止")
                        break
                    elif current_distance < safe_distance:
                        rospy.loginfo(f"⚠️ 距离监控：距离过近{current_distance:.2f}m，紧急停止")
                        break

            self.vel_pub.publish(twist)
            time.sleep(0.5)
            if i % 4 == 0:  # 每2秒打印一次状态
                rospy.loginfo(f"   执行中... {i*0.5:.1f}/{execution_time:.1f}秒")

        # 停止
        stop_twist = Twist()
        rospy.loginfo("🛑 发布停止指令")
        self.vel_pub.publish(stop_twist)
        rospy.loginfo(f"✅ 2D行为 {behavior} (TrackID {track_id}) 完成")

    def _calculate_execution_time(self, behavior, distance, twist):
        """基于物理学的精确执行时间计算 - 完全参考V7版本思路"""

        # 获取配置参数
        stop_time = self.config.execution_time_params.get('stop_time', 1.0)
        other_time = self.config.execution_time_params.get('other_behavior_time', 4.0)
        min_time = self.config.execution_time_params.get('min_time', 2.0)
        max_time = self.config.execution_time_params.get('max_time', 10.0)
        time_buffer = self.config.execution_time_params.get('time_buffer', 0.5)
        min_speed = self.config.execution_time_params.get('min_speed', 0.1)

        if behavior == "stop":
            # 停止指令：固定时间
            execution_time = stop_time
            rospy.loginfo(f"⏱️ 停止指令执行时间: {execution_time:.1f}s")

        elif behavior in ["approach", "retreat"]:
            # 靠近和后退：根据距离和实际速度精确计算
            # 获取实际运动速度的最大分量
            max_speed = max(abs(twist.linear.x), abs(twist.linear.y), min_speed)

            # 关键改进：计算有效移动距离（减去安全距离）
            if behavior == "approach":
                target_distance = self.config.spatial_params.get('target_distance', 0.8)
                effective_distance = max(distance - target_distance, 0.01)  # 至少保留0.5m移动距离
                rospy.loginfo(f"🎯 approach安全计算: 总距离{distance:.2f}m - 目标距离{target_distance:.1f}m = 有效距离{effective_distance:.2f}m")
            elif behavior == "retreat":
                # retreat通常是远离，使用原始距离
                effective_distance = distance
                rospy.loginfo(f"🔙 retreat计算: 使用原始距离{effective_distance:.2f}m")

            # 物理公式：时间 = 有效距离 ÷ 速度
            calculated_time = effective_distance / max_speed

            # 限制在合理范围内，并减去缓冲时间
            execution_time = min(max(calculated_time, min_time), max_time) - time_buffer

            rospy.loginfo(f"⏱️ 【时间计算】{behavior}: {effective_distance:.2f}m ÷ {max_speed:.3f}m/s = {calculated_time:.1f}s")
            rospy.loginfo(f"⏱️ 【最终时间】{execution_time:.1f}s (缓冲-{time_buffer}s)")

        else:
            # 其他行为：使用固定时间（完全按照V7逻辑）
            execution_time = other_time
            rospy.loginfo(f"⏱️ {behavior}固定执行时间: {execution_time:.1f}s")

        return execution_time
