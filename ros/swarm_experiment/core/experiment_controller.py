#!/usr/bin/env python3
"""
实验控制器模块
负责协调各个模块，实现完整的集群实验流程
"""

import rospy
import time
from .config import SwarmConfig
from ..detection.multi_target_detector import MultiTargetDetector
from ..analysis.flash_analyzer import FlashAnalyzer
from ..analysis.spatial_analyzer import SpatialAnalyzer
from ..behavior.swarm_behavior import SwarmBehavior
from ..utils.ros_utils import ROSUtils

class ExperimentController:
    """实验控制器"""
    
    def __init__(self):
        rospy.init_node('simple_2d_swarm_experiment', anonymous=True)

        # 初始化配置
        self.config = SwarmConfig()

        # 初始化ROS工具
        self.ros_utils = ROSUtils(self.config)
        publishers = self.ros_utils.get_publishers()

        # 初始化各个模块
        self.detector = MultiTargetDetector(self.config)
        self.flash_analyzer = FlashAnalyzer(self.config)
        self.spatial_analyzer = SpatialAnalyzer(self.config)
        self.behavior_executor = SwarmBehavior(self.config, publishers['velocity'])

        # 实验状态
        self.detected_targets_2d = []
        self.track_targets_2d = {}

        # 自主模式状态
        self.autonomous_active = False
        self.command_cooldowns = {}  # 格式: {(track_id, command): cooldown_end_time}
        self.cooldown_duration = 8.0  # 8秒冷却时间
        self.cycle_sleep_duration = 0.2  # 循环间隔0.2秒

        rospy.loginfo(f"🚀 实验控制器初始化完成 - 节点: {self.config.node_name}")
        
    def start_2d_detection(self):
        """启动2D空间检测"""
        # 清空之前的检测结果
        self.detected_targets_2d = []
        self.track_targets_2d = {}
        
        # 启动多摄像头检测
        all_camera_detections = self.detector.start_2d_detection()
        
        # 分析2D空间信息
        self.analyze_2d_spatial_info_multi_target(all_camera_detections)
    
    def analyze_2d_spatial_info_multi_target(self, all_camera_detections):
        """分析2D空间信息 - 批量优化版本"""
        rospy.loginfo("🧠 分析2D空间信息（批量优化版本）...")

        # 检查是否有缓存的检测结果
        if not all_camera_detections:
            rospy.logwarn("❌ 未找到检测结果缓存")
            return

        # 1. 先对所有检测到的目标进行空间计算并输出
        self.spatial_analyzer.analyze_spatial_info(all_camera_detections)

        # 2. 进行灯语分析，找出有效指令的目标
        valid_targets = self.flash_analyzer.analyze_flash_patterns(all_camera_detections)
        
        if not valid_targets:
            return

        # 3. 只对有效指令的目标进行空间计算
        candidates = self.spatial_analyzer.calculate_target_spatial_info(all_camera_detections, valid_targets)
        
        if not candidates:
            return

        # 4. 选择最优目标并执行
        best_target = self.spatial_analyzer.select_best_candidate(candidates)
        
        if best_target:
            # 执行最优目标的行为
            self.behavior_executor.execute_behavior(best_target)

            # 如果是自主模式，记录冷却时间
            if self.autonomous_active:
                self._record_command_cooldown(best_target)
    
    def show_2d_params(self):
        """显示2D参数"""
        print("\n⚙️ 2D空间参数:")
        for key, value in self.config.spatial_params.items():
            print(f"  {key}: {value}")

        print("\n📹 摄像头朝向配置:")
        for cam, angle in self.config.camera_orientations.items():
            direction = self.config.get_direction_description(angle)
            print(f"  {cam}: {angle}° ({direction})")
            
        print("\n🔧 检测配置:")
        print(f"  detection_duration: {self.config.config['detection_duration']}s")
        print(f"  real_width: {self.config.config['real_width']}m")
        
        print("\n⏹️ 智能早停配置:")
        print(f"  early_stop_enabled: {self.config.config.get('early_stop_enabled', False)}")
        if self.config.config.get('early_stop_enabled', False):
            print(f"  early_stop_no_target_duration: {self.config.config.get('early_stop_no_target_duration', 2.0)}s")
            print(f"  min_detection_duration: {self.config.config.get('min_detection_duration', 1.0)}s")
        else:
            print("  (智能早停已禁用)")
            
        print("\n🔆 灯语分析配置:")
        print(f"  flash_num_frames: {self.config.config.get('flash_num_frames', 36)}帧")
        print(f"  flash_start_frame: {self.config.config.get('flash_start_frame', 7)}")
        print(f"  flash_threshold: {self.config.config.get('flash_threshold', 0.2)}")

        print("\n⏱️ 物理学执行时间配置:")
        print(f"  停止指令: {self.config.execution_time_params.get('stop_time', 1.0)}s")
        print(f"  其他行为: {self.config.execution_time_params.get('other_behavior_time', 4.0)}s")
        print(f"  approach/retreat: 距离÷速度计算")
        print(f"  计算范围: {self.config.execution_time_params.get('min_time', 2.0)}-{self.config.execution_time_params.get('max_time', 10.0)}s")
        print(f"  时间缓冲: -{self.config.execution_time_params.get('time_buffer', 0.5)}s")
        print(f"  最小速度: {self.config.execution_time_params.get('min_speed', 0.1)}m/s")

    def adjust_2d_params(self):
        """调整2D参数"""
        print("\n🔧 调整2D参数")
        print("="*50)
        print("1. 空间计算参数")
        print("2. 检测配置参数")
        print("3. 智能早停参数")
        print("="*50)
        
        choice = input("选择参数类型 (1-3): ").strip()
        
        if choice == '1':
            self.show_2d_params()
            param = input("输入要调整的参数名: ").strip()
            if param in self.config.spatial_params:
                try:
                    value = float(input(f"输入新值 (当前: {self.config.spatial_params[param]}): "))
                    self.config.update_spatial_param(param, value)
                    print(f"✅ {param} 已更新为 {value}")
                except ValueError:
                    print("❌ 无效数值")
        
        elif choice == '2':
            print(f"\n📹 检测配置参数:")
            print(f"  detection_duration: {self.config.config['detection_duration']}s")
            print(f"  real_width: {self.config.config['real_width']}m")
            
            param = input("输入要调整的参数名 (detection_duration/real_width): ").strip()
            if param in ['detection_duration', 'real_width']:
                try:
                    value = float(input(f"输入新值 (当前: {self.config.config[param]}): "))
                    self.config.update_config(param, value)
                    print(f"✅ {param} 已更新为 {value}")
                except ValueError:
                    print("❌ 无效数值")
        
        elif choice == '3':
            print(f"\n⏹️ 智能早停参数:")
            print(f"  early_stop_enabled: {self.config.config.get('early_stop_enabled', False)}")
            print(f"  early_stop_no_target_duration: {self.config.config.get('early_stop_no_target_duration', 2.0)}s")
            print(f"  min_detection_duration: {self.config.config.get('min_detection_duration', 1.0)}s")
            
            param = input("输入要调整的参数名: ").strip()
            if param == 'early_stop_enabled':
                value = input(f"启用智能早停? (y/n, 当前: {self.config.config.get(param, False)}): ").strip().lower()
                self.config.update_config(param, value in ['y', 'yes', 'true', '1'])
                print(f"✅ {param} 已更新为 {self.config.config[param]}")
            elif param in ['early_stop_no_target_duration', 'min_detection_duration']:
                try:
                    value = float(input(f"输入新值 (当前: {self.config.config.get(param, 0)}): "))
                    self.config.update_config(param, value)
                    print(f"✅ {param} 已更新为 {value}")
                except ValueError:
                    print("❌ 无效数值")
            else:
                print("❌ 无效参数名")
        else:
            print("❌ 无效选择")

    def show_detected_targets_2d(self):
        """显示2D检测目标"""
        detection_results = self.detector.get_detection_results()
        
        print(f"\n🎯 2D检测结果:")
        if detection_results:
            for cam_name, detections in detection_results.items():
                total_detections = sum(len(dets) for _, dets in detections)
                print(f"  {cam_name}: {len(detections)}帧, {total_detections}个检测")
        else:
            print("  暂无检测结果")

    def run_experiment(self):
        """运行实验（增强菜单 - 支持自主模式）"""
        while not rospy.is_shutdown():
            print("\n" + "="*70)
            print("🚗 简化2D空间感知灯语集群实验 - 自主智能体版本")
            print(f"节点: {self.config.node_name}")
            if self.autonomous_active:
                print("🤖 当前状态: 自主模式运行中")
                cooldowns = self.get_cooldown_status()
                if cooldowns:
                    print(f"❄️ 活跃冷却: {len(cooldowns)}个")
            else:
                print("🔧 当前状态: 交互模式")
            print("="*70)
            print("实验模式:")
            print("  1. 启动2D空间检测（多目标模式）")
            print("  2. 查看2D空间参数")
            print("  3. 调整2D参数")
            print("  4. 显示检测目标（多目标分析）")
            print("  5. 🤖 启动自主模式（连续智能感知）")
            print("  6. 🛑 停止自主模式")
            print("  7. 📊 查看冷却状态")
            print("  q. 退出")
            print("="*70)

            try:
                choice = input("请选择: ").strip()

                if choice == '1':
                    if not self.autonomous_active:
                        self.start_2d_detection()
                    else:
                        print("⚠️ 自主模式运行中，请先停止自主模式")
                elif choice == '2':
                    self.show_2d_params()
                elif choice == '3':
                    if not self.autonomous_active:
                        self.adjust_2d_params()
                    else:
                        print("⚠️ 自主模式运行中，无法调整参数")
                elif choice == '4':
                    self.show_detected_targets_2d()
                elif choice == '5':
                    if not self.autonomous_active:
                        print("🤖 启动自主模式...")
                        self.start_autonomous_mode()
                    else:
                        print("⚠️ 自主模式已在运行中")
                elif choice == '6':
                    self.stop_autonomous_mode()
                elif choice == '7':
                    self._show_cooldown_status()
                elif choice.lower() == 'q':
                    if self.autonomous_active:
                        print("🛑 停止自主模式...")
                        self.stop_autonomous_mode()
                    print("退出实验")
                    break
                else:
                    print("无效选择，请重新输入")

            except KeyboardInterrupt:
                print("\n实验中断")
                break

    def _record_command_cooldown(self, target_info):
        """记录指令冷却时间"""
        track_id = target_info['track_id']
        command = self.config.SWARM_COMMANDS[target_info['number']]
        cooldown_key = (track_id, command)
        cooldown_end_time = time.time() + self.cooldown_duration

        self.command_cooldowns[cooldown_key] = cooldown_end_time
        rospy.loginfo(f"🕒 记录冷却: TrackID{track_id}-{command} 冷却至 {time.strftime('%H:%M:%S', time.localtime(cooldown_end_time))}")

    def _is_command_in_cooldown(self, track_id, command):
        """检查指令是否在冷却期内"""
        cooldown_key = (track_id, command)
        if cooldown_key not in self.command_cooldowns:
            return False

        current_time = time.time()
        cooldown_end_time = self.command_cooldowns[cooldown_key]

        if current_time >= cooldown_end_time:
            # 冷却结束，移除记录
            del self.command_cooldowns[cooldown_key]
            return False

        return True

    def _cleanup_expired_cooldowns(self):
        """清理过期的冷却记录"""
        current_time = time.time()
        expired_keys = [key for key, end_time in self.command_cooldowns.items()
                       if current_time >= end_time]

        for key in expired_keys:
            del self.command_cooldowns[key]

    def start_autonomous_mode(self):
        """启动自主模式"""
        rospy.loginfo("🤖 启动自主模式 - 连续环境感知与智能决策")
        rospy.loginfo("📡 4摄像头360°持续扫描 | ⚡智能早停(2s无目标) | 🔄8秒指令冷却")

        self.autonomous_active = True
        cycle_count = 0

        try:
            while not rospy.is_shutdown() and self.autonomous_active:
                cycle_count += 1
                cycle_start_time = time.time()

                rospy.loginfo(f"\n🔄 自主循环 #{cycle_count} 开始")

                # 清理过期冷却记录
                self._cleanup_expired_cooldowns()

                # 执行一轮检测分析
                self._autonomous_detection_cycle()

                # 计算循环耗时
                cycle_duration = time.time() - cycle_start_time
                rospy.loginfo(f"⏱️ 循环#{cycle_count}耗时: {cycle_duration:.2f}s")

                # 休眠0.2秒后开始下一轮
                rospy.loginfo(f"😴 休眠{self.cycle_sleep_duration}s后开始下一轮...")
                time.sleep(self.cycle_sleep_duration)

        except KeyboardInterrupt:
            rospy.loginfo("🛑 自主模式被用户中断")
        except Exception as e:
            rospy.logerr(f"❌ 自主模式异常: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.autonomous_active = False
            rospy.loginfo("🏁 自主模式结束")

    def _autonomous_detection_cycle(self):
        """自主检测循环 - 单次完整的检测分析执行周期"""
        # 1. 启动多摄像头检测（带智能早停）
        rospy.loginfo("\n" + "="*60)
        rospy.loginfo("🎥 启动360°环境扫描...")
        all_camera_detections = self.detector.start_2d_detection()

        # 2. 检查是否有检测结果
        if not all_camera_detections:
            rospy.loginfo("📭 本轮无检测结果")
            rospy.loginfo("="*60)
            return

        # 3. 空间信息分析
        self.spatial_analyzer.analyze_spatial_info(all_camera_detections)

        # 4. 灯语分析，找出有效指令
        rospy.loginfo("💡 分析灯语模式...")
        valid_targets = self.flash_analyzer.analyze_flash_patterns(all_camera_detections)

        if not valid_targets:
            rospy.loginfo("💡 本轮无有效灯语指令")
            rospy.loginfo("="*60)
            return

        # 5. 应用冷却过滤
        filtered_targets = self._filter_targets_by_cooldown(valid_targets)

        if not filtered_targets:
            rospy.loginfo("❄️ 所有有效指令均在冷却期内")
            return

        # 6. 计算空间信息并选择最优目标
        candidates = self.spatial_analyzer.calculate_target_spatial_info(all_camera_detections, filtered_targets)

        if not candidates:
            rospy.loginfo("📐 空间计算无有效候选")
            return

        # 7. 选择并立即执行最优目标
        best_target = self.spatial_analyzer.select_best_candidate(candidates)

        if best_target:
            rospy.loginfo("🎯" + "="*50)
            rospy.loginfo(f"🎯 【执行指令】TrackID{best_target['track_id']} - {self.config.SWARM_COMMANDS[best_target['number']]}")
            rospy.loginfo(f"📏 距离: {best_target['distance']:.2f}m | 角度: {best_target['azimuth']:.1f}°")
            rospy.loginfo("🎯" + "="*50)

            # 立即执行，无需用户确认
            self.behavior_executor.execute_behavior(best_target)

            # 记录冷却时间
            self._record_command_cooldown(best_target)
            rospy.loginfo("="*60)
        else:
            rospy.loginfo("🤔 未找到最优执行目标")
            rospy.loginfo("="*60)

    def _filter_targets_by_cooldown(self, valid_targets):
        """根据冷却时间过滤目标"""
        filtered_targets = []

        for target in valid_targets:
            cam_name, track_id, pattern, number, behavior, priority = target

            if not self._is_command_in_cooldown(track_id, behavior):
                filtered_targets.append(target)
                rospy.loginfo(f"✅ 目标通过冷却检查: TrackID{track_id}-{behavior}")
            else:
                # 计算剩余冷却时间
                cooldown_key = (track_id, behavior)
                remaining_time = self.command_cooldowns[cooldown_key] - time.time()
                rospy.loginfo(f"❄️ 目标在冷却期: TrackID{track_id}-{behavior} (剩余{remaining_time:.1f}s)")

        rospy.loginfo(f"🔍 冷却过滤结果: {len(filtered_targets)}/{len(valid_targets)} 个目标可执行")
        return filtered_targets

    def stop_autonomous_mode(self):
        """停止自主模式"""
        if self.autonomous_active:
            rospy.loginfo("🛑 正在停止自主模式...")
            self.autonomous_active = False
        else:
            rospy.loginfo("ℹ️ 自主模式未运行")

    def is_autonomous_active(self):
        """检查自主模式是否激活"""
        return self.autonomous_active

    def get_cooldown_status(self):
        """获取当前冷却状态"""
        current_time = time.time()
        active_cooldowns = []

        for (track_id, command), end_time in self.command_cooldowns.items():
            remaining_time = end_time - current_time
            if remaining_time > 0:
                active_cooldowns.append({
                    'track_id': track_id,
                    'command': command,
                    'remaining_time': remaining_time
                })

        return active_cooldowns

    def _show_cooldown_status(self):
        """显示当前冷却状态"""
        cooldowns = self.get_cooldown_status()

        print(f"\n❄️ 当前冷却状态:")
        print("="*50)

        if not cooldowns:
            print("  无活跃冷却")
        else:
            print(f"  活跃冷却数量: {len(cooldowns)}")
            print("  详细信息:")
            for cooldown in cooldowns:
                track_id = cooldown['track_id']
                command = cooldown['command']
                remaining = cooldown['remaining_time']
                print(f"    TrackID{track_id}-{command}: 剩余{remaining:.1f}秒")

        print("="*50)
