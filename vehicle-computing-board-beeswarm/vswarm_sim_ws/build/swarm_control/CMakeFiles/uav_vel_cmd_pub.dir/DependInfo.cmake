# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_control/src/uav_vel_cmd_pub.cpp" "/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/swarm_control/CMakeFiles/uav_vel_cmd_pub.dir/src/uav_vel_cmd_pub.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "BOOST_TEST_DYN_LINK"
  "LIBBULLET_VERSION=2.88"
  "LIBBULLET_VERSION_GT_282"
  "ROSCONSOLE_BACKEND_LOG4CXX"
  "ROS_BUILD_SHARED_LIBS=1"
  "ROS_PACKAGE_NAME=\"swarm_control\""
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/devel/include"
  "/opt/ros/noetic/include"
  "/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp"
  "/usr/include/eigen3"
  "/usr/include/gazebo-11"
  "/usr/include/bullet"
  "/usr/include/simbody"
  "/usr/include/sdformat-9.10"
  "/usr/include/ignition/math6"
  "/usr/include/OGRE"
  "/usr/include/OGRE/Terrain"
  "/usr/include/OGRE/Paging"
  "/usr/include/ignition/transport8"
  "/usr/include/ignition/msgs5"
  "/usr/include/ignition/common3"
  "/usr/include/ignition/fuel_tools4"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
