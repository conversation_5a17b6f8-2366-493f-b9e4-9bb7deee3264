#!/usr/bin/env python3
"""
解耦系统监控工具 - 监控解耦群体机器人系统的运行状态
"""

import rospy
import time
from collections import defaultdict, deque
from std_msgs.msg import Header
from swarm_experiment.msg import DetectionArray, FlashPatternArray, SpatialInfoArray, SwarmCommand

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        rospy.init_node('system_monitor', anonymous=True)
        
        # 统计数据
        self.stats = {
            'detection_count': 0,
            'flash_count': 0,
            'spatial_count': 0,
            'command_count': 0,
            'detection_rate': deque(maxlen=50),
            'flash_rate': deque(maxlen=50),
            'spatial_rate': deque(maxlen=50),
            'command_rate': deque(maxlen=50)
        }
        
        self.last_stats_time = time.time()
        self.last_counts = {
            'detection': 0,
            'flash': 0,
            'spatial': 0,
            'command': 0
        }
        
        # 最近活动
        self.recent_data = {
            'detections': deque(maxlen=10),
            'flash_patterns': deque(maxlen=10),
            'spatial_infos': deque(maxlen=10),
            'commands': deque(maxlen=10)
        }
        
        # 订阅器
        self.detection_sub = rospy.Subscriber('/detection_results', DetectionArray, self._detection_callback)
        self.flash_sub = rospy.Subscriber('/flash_patterns', FlashPatternArray, self._flash_callback)
        self.spatial_sub = rospy.Subscriber('/spatial_info', SpatialInfoArray, self._spatial_callback)
        self.command_sub = rospy.Subscriber('/swarm_commands', SwarmCommand, self._command_callback)
        
        rospy.loginfo("📊 解耦系统监控器启动")
    
    def _detection_callback(self, msg):
        """检测结果回调"""
        self.stats['detection_count'] += len(msg.detections)
        self.recent_data['detections'].append({
            'timestamp': time.time(),
            'count': len(msg.detections),
            'cameras': list(set(d.camera_name for d in msg.detections))
        })
    
    def _flash_callback(self, msg):
        """灯语模式回调"""
        self.stats['flash_count'] += len(msg.patterns)
        self.recent_data['flash_patterns'].append({
            'timestamp': time.time(),
            'count': len(msg.patterns),
            'commands': [(p.command_name, p.confidence) for p in msg.patterns]
        })
    
    def _spatial_callback(self, msg):
        """空间信息回调"""
        self.stats['spatial_count'] += len(msg.spatial_infos)
        self.recent_data['spatial_infos'].append({
            'timestamp': time.time(),
            'count': len(msg.spatial_infos),
            'avg_distance': sum(s.distance for s in msg.spatial_infos) / len(msg.spatial_infos) if msg.spatial_infos else 0
        })
    
    def _command_callback(self, msg):
        """群体指令回调"""
        self.stats['command_count'] += 1
        self.recent_data['commands'].append({
            'timestamp': time.time(),
            'command': msg.command_name,
            'distance': msg.target_distance,
            'azimuth': msg.target_azimuth
        })
    
    def print_status(self):
        """打印系统状态"""
        current_time = time.time()
        time_diff = current_time - self.last_stats_time
        
        if time_diff >= 1.0:  # 每秒计算一次速率
            # 计算速率
            detection_rate = (self.stats['detection_count'] - self.last_counts['detection']) / time_diff
            flash_rate = (self.stats['flash_count'] - self.last_counts['flash']) / time_diff
            spatial_rate = (self.stats['spatial_count'] - self.last_counts['spatial']) / time_diff
            command_rate = (self.stats['command_count'] - self.last_counts['command']) / time_diff
            
            # 更新速率队列
            self.stats['detection_rate'].append(detection_rate)
            self.stats['flash_rate'].append(flash_rate)
            self.stats['spatial_rate'].append(spatial_rate)
            self.stats['command_rate'].append(command_rate)
            
            # 更新计数
            self.last_counts['detection'] = self.stats['detection_count']
            self.last_counts['flash'] = self.stats['flash_count']
            self.last_counts['spatial'] = self.stats['spatial_count']
            self.last_counts['command'] = self.stats['command_count']
            
            self.last_stats_time = current_time
        
        print("\n" + "="*80)
        print("🤖 解耦群体机器人系统监控状态")
        print("="*80)
        
        # 总体统计
        print(f"📊 总体统计:")
        print(f"   检测结果: {self.stats['detection_count']} 个")
        print(f"   灯语模式: {self.stats['flash_count']} 个")
        print(f"   空间信息: {self.stats['spatial_count']} 个")
        print(f"   群体指令: {self.stats['command_count']} 个")
        
        # 实时速率
        if self.stats['detection_rate']:
            avg_detection_rate = sum(self.stats['detection_rate']) / len(self.stats['detection_rate'])
            avg_flash_rate = sum(self.stats['flash_rate']) / len(self.stats['flash_rate'])
            avg_spatial_rate = sum(self.stats['spatial_rate']) / len(self.stats['spatial_rate'])
            avg_command_rate = sum(self.stats['command_rate']) / len(self.stats['command_rate'])
            
            print(f"\n⚡ 实时速率 (平均):")
            print(f"   检测速率: {avg_detection_rate:.1f} 个/秒")
            print(f"   灯语速率: {avg_flash_rate:.1f} 个/秒")
            print(f"   空间速率: {avg_spatial_rate:.1f} 个/秒")
            print(f"   指令速率: {avg_command_rate:.1f} 个/秒")
        
        # 最近活动
        print(f"\n🔄 最近活动:")
        
        if self.recent_data['detections']:
            latest_detection = self.recent_data['detections'][-1]
            print(f"   最新检测: {latest_detection['count']} 个目标，摄像头: {latest_detection['cameras']}")
        
        if self.recent_data['flash_patterns']:
            latest_flash = self.recent_data['flash_patterns'][-1]
            commands = [f"{cmd}({conf:.2f})" for cmd, conf in latest_flash['commands']]
            print(f"   最新灯语: {latest_flash['count']} 个模式，指令: {commands}")
        
        if self.recent_data['spatial_infos']:
            latest_spatial = self.recent_data['spatial_infos'][-1]
            print(f"   最新空间: {latest_spatial['count']} 个目标，平均距离: {latest_spatial['avg_distance']:.2f}m")
        
        if self.recent_data['commands']:
            latest_command = self.recent_data['commands'][-1]
            print(f"   最新指令: {latest_command['command']}，距离: {latest_command['distance']:.2f}m，方位: {latest_command['azimuth']:.1f}°")
        
        # 系统健康状态
        print(f"\n💚 系统健康:")
        health_status = self._check_system_health()
        for component, status in health_status.items():
            status_icon = "✅" if status else "❌"
            print(f"   {component}: {status_icon}")
        
        print("="*80)
    
    def _check_system_health(self):
        """检查系统健康状态"""
        current_time = time.time()
        health = {}
        
        # 检查各组件是否有最近的数据
        health['视觉检测'] = (self.recent_data['detections'] and 
                           current_time - self.recent_data['detections'][-1]['timestamp'] < 10.0)
        
        health['灯语分析'] = (self.recent_data['flash_patterns'] and 
                           current_time - self.recent_data['flash_patterns'][-1]['timestamp'] < 15.0)
        
        health['空间分析'] = (self.recent_data['spatial_infos'] and 
                           current_time - self.recent_data['spatial_infos'][-1]['timestamp'] < 10.0)
        
        health['行为决策'] = (self.recent_data['commands'] and 
                           current_time - self.recent_data['commands'][-1]['timestamp'] < 20.0)
        
        return health
    
    def run(self):
        """运行监控器"""
        rate = rospy.Rate(0.2)  # 每5秒更新一次
        
        while not rospy.is_shutdown():
            try:
                self.print_status()
                rate.sleep()
            except KeyboardInterrupt:
                break
            except Exception as e:
                rospy.logwarn(f"监控错误: {e}")

def main():
    try:
        monitor = SystemMonitor()
        monitor.run()
    except rospy.ROSInterruptException:
        pass

if __name__ == '__main__':
    main()
